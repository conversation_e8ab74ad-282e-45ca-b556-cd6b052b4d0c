面试官你好，我叫谢荣飞，从事前端行业有六年了，主要使用的技术栈就vue全家桶

最近做的两个项目第一个是政府类的项目，分为前台后台，然后前台有pc和h5，后台有pc和小程序端，上海市民查询法规的，主要的就是查询搜索功能

第二个项目就是POC项⽬管理平台，有比如表单的创建，不同⻆⾊的审批信息，权限控制这方面的功能。

在POC项目管理平台中，一套完整的多级权限控制系统，主要通过以下技术手段：

我采用了基于RBAC模型的三层权限结构设计，使用**Vuex**存储用户权限状态。在Vuex中，我定义了包含roleCode、permissions和flowStatus的state，并通过mutations如SET_ROLE_CODE和SET_PERMISSIONS管理状态更新。关键是我封装了hasPermission这个getter方法，它能够根据权限码和流程状态动态判断权限。

为了在前端实现权限控制，我封装了名为**v-permission**的**Vue自定义指令**，通过directive API实现DOM元素的权限控制。这个指令在mounted钩子中检查用户是否拥有特定权限，如果没有权限，会直接从DOM中移除元素或设置display为none。我还添加了warning修饰符，当用户尝试操作无权限元素时，使用**Element-UI的ElMessage组件**弹出友好提示。

```jsx
// directives/permission.js
import store from '@/store'
import { ElMessage } from 'element-plus'

export default {
  // Vue3指令钩子，在元素挂载时执行
  mounted(el, binding) {
    // 解构获取指令的值和修饰符
    const { value, modifiers } = binding
    const hasWarning = modifiers.warning // 检查是否有warning修饰符
    
    // 从指令值中提取权限编码和流程状态
    // 支持两种使用方式：v-permission="'edit'" 或 v-permission="{permission: 'edit', flowStatus: 'pending'}"
    let permissionCode, flowStatus
    if (typeof value === 'object') {
      permissionCode = value.permission
      flowStatus = value.flowStatus
    } else {
      permissionCode = value
    }
    
    // 调用store中的getters检查权限
    // 同时考虑了角色权限和流程状态两个维度
    const hasPermission = store.getters['permission/hasPermission'](
      permissionCode, 
      flowStatus
    )
    
    // 权限验证失败时的处理逻辑
    if (!hasPermission) {
      // 无权限时直接从DOM中移除元素，保证界面安全
      if (el.parentNode) {
        el.parentNode.removeChild(el)
      } else {
        // 如果无法访问父节点，则隐藏元素
        el.style.display = 'none'
      }
      
      // 如果设置了warning修饰符，添加警告提示功能
      // 这对于那些视觉上需要保留但无权操作的元素很有用
      if (hasWarning) {
        // 通过事件捕获确保点击拦截发生在其他事件处理之前
        el.addEventListener('click', (e) => {
          e.stopPropagation() // 阻止事件冒泡
          e.preventDefault() // 阻止默认行为
          ElMessage.warning('您没有权限执行此操作') // 显示友好提示
        }, true)
      }
    }
  }
}
```

在组件中，我大量应用了**Vue3的Composition API**，通过defineComponent、ref、computed等API构建响应式UI。结合**Element Plus**的组件，实现了审批流程界面。以下是在组件中使用权限指令的示例：

```html
<template>
  <div class="approval-actions">
    <!-- 基本权限控制：只有拥有approve权限的用户才能看到此按钮 -->
    <el-button 
      type="primary" 
      v-permission="'approve'"
      @click="handleApprove"
    >
      审批通过
    </el-button>
    
    <!-- 复杂权限控制：结合流程状态的权限控制 -->
    <!-- 只有在"待审批"状态下才显示，且用户必须拥有reject权限 -->
    <el-button 
      type="danger" 
      v-permission="{permission: 'reject', flowStatus: 'pending'}"
      @click="handleReject"
    >
      驳回申请
    </el-button>
    
    <!-- 带警告提示的权限控制 -->
    <!-- 按钮仍会显示，但点击时会提示无权限 -->
    <el-button 
      type="warning" 
      v-permission.warning="'delete'"
      @click="handleDelete"
    >
      删除项目
    </el-button>
  </div>
</template>
```

这套实现让不同角色在不同审批状态下拥有精确的操作权限，既保证了系统安全性，又极大提升了用户体验。通过自定义指令的方式，我将权限控制逻辑与业务组件解耦，使代码更加清晰易维护，同时也提高了开发效率，因为开发人员只需关注业务逻辑而无需重复编写权限判断代码。

## **可定制化组件库开发实现话术**

在POC项目管理平台中，我设计并开发了20多个可定制化组件，包括各类表单、表格和高级搜索组件，这些组件极大提升了团队的开发效率。

我基于**Element UI**进行了二次封装，采用了**Vue3 Composition API**和**TypeScript**确保类型安全。通过提取公共逻辑，我实现了高度可配置的组件系统，使开发人员只需要传入不同配置即可生成复杂的交互界面。

特别是在表单组件的开发上，我创新性地使用了**JSON Schema**驱动的表单渲染方案：

```html
// components/DynamicForm/index.vue
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    :label-width="schema.labelWidth || '120px'"
  >
    <!-- 动态渲染表单项 -->
    <template v-for="field in schema.fields" :key="field.prop">
      <!-- 根据字段类型渲染不同组件 -->
      <el-form-item
        :label="field.label"
        :prop="field.prop"
        :required="field.required"
      >
        <!-- 输入框类型 -->
        <el-input
          v-if="field.type === 'input'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          :maxlength="field.maxlength"
          :show-word-limit="field.showWordLimit"
          @change="handleFieldChange(field.prop, $event)"
        />
        
        <!-- 选择器类型 -->
        <el-select
          v-else-if="field.type === 'select'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          :filterable="field.filterable"
          :multiple="field.multiple"
          :remote="field.remote"
          :remote-method="field.remote ? remoteMethod : undefined"
          @change="handleFieldChange(field.prop, $event)"
        >
          <el-option
            v-for="option in getOptions(field)"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            :disabled="option.disabled"
          />
        </el-select>
        
        <!-- 更多类型组件... -->
        
        <!-- 自定义渲染插槽 -->
        <slot
          v-else-if="field.type === 'custom'"
          :name="field.slotName"
          :field="field"
          :form-data="formData"
        />
      </el-form-item>
    </template>
    
    <!-- 表单操作按钮区域 -->
    <el-form-item v-if="schema.showActions !== false">
      <slot name="actions">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </slot>
    </el-form-item>
  </el-form>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, onMounted } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { cloneDeep } from 'lodash-es'

// 表单字段类型定义
interface FormField {
  type: string;                // 字段类型：input, select, date 等
  prop: string;                // 字段属性名
  label: string;               // 标签文本
  placeholder?: string;        // 占位文本
  required?: boolean;          // 是否必填
  disabled?: boolean;          // 是否禁用
  options?: Array<{            // 选项数据(用于select等)
    label: string;
    value: any;
    disabled?: boolean;
  }>;
  rules?: Array<any>;          // 验证规则
  defaultValue?: any;          // 默认值
  slotName?: string;           // 自定义插槽名
  // 其他特有属性...
  [key: string]: any;
}

// 表单Schema定义
interface FormSchema {
  fields: FormField[];         // 表单字段配置
  labelWidth?: string;         // 标签宽度
  showActions?: boolean;       // 是否显示操作按钮
  // 更多配置项...
}

export default defineComponent({
  name: 'DynamicForm',
  
  props: {
    // 表单配置Schema
    schema: {
      type: Object as () => FormSchema,
      required: true
    },
    // 表单初始值
    initialValues: {
      type: Object,
      default: () => ({})
    }
  },
  
  emits: ['submit', 'reset', 'field-change'],
  
  setup(props, { emit }) {
    // 表单引用
    const formRef = ref<FormInstance>()
    
    // 初始化表单数据
    const formData = reactive<Record<string, any>>({})
    
    // 计算表单规则
    const formRules = computed(() => {
      const rules: FormRules = {}
      
      props.schema.fields.forEach(field => {
        if (field.rules) {
          rules[field.prop] = field.rules
        } else if (field.required) {
          // 自动为必填字段添加必填规则
          rules[field.prop] = [
            { required: true, message: `请${field.type === 'select' ? '选择' : '输入'}${field.label}`, trigger: field.type === 'select' ? 'change' : 'blur' }
          ]
        }
      })
      
      return rules
    })
    
    // 初始化表单数据
    onMounted(() => {
      // 设置字段默认值
      props.schema.fields.forEach(field => {
        if (props.initialValues[field.prop] !== undefined) {
          // 优先使用传入的初始值
          formData[field.prop] = props.initialValues[field.prop]
        } else if (field.defaultValue !== undefined) {
          // 其次使用字段定义的默认值
          formData[field.prop] = field.defaultValue
        } else {
          // 最后设置为空值
          formData[field.prop] = field.multiple ? [] : ''
        }
      })
    })
    
    // 获取下拉选项
    const getOptions = (field: FormField) => {
      return field.options || []
    }
    
    // 远程搜索方法
    const remoteMethod = (query: string) => {
      // 实现远程搜索逻辑
      console.log('远程搜索', query)
    }
    
    // 表单项变更处理
    const handleFieldChange = (prop: string, value: any) => {
      emit('field-change', { prop, value, formData: cloneDeep(formData) })
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        emit('submit', cloneDeep(formData))
      } catch (error) {
        console.error('表单验证失败', error)
      }
    }
    
    // 重置表单
    const resetForm = () => {
      if (!formRef.value) return
      formRef.value.resetFields()
      emit('reset')
    }
    
    return {
      formRef,
      formData,
      formRules,
      getOptions,
      remoteMethod,
      handleFieldChange,
      submitForm,
      resetForm
    }
  }
})
</script>
```

这个动态表单组件是我设计的核心组件之一。它通过**JSON配置驱动**的方式实现高度定制化。我特别关注了以下几点：

1. **类型安全**：使用TypeScript定义清晰的接口，确保配置项类型安全。
1. **高扩展性**：支持自定义插槽渲染，可以无限扩展表单项类型。
1. **事件机制**：提供完善的事件回调，包括字段变更、表单提交和重置事件。
1. **数据验证**：支持Element Plus的表单验证规则，同时可以自动为必填字段生成验证规则。
1. **响应式设计**：利用Vue3的reactive和computed API实现高效的响应式更新。

1. 1. **类型安全**：使用TypeScript定义清晰的接口，确保配置项类型安全。
2. 1. **高扩展性**：支持自定义插槽渲染，可以无限扩展表单项类型。
3. 1. **事件机制**：提供完善的事件回调，包括字段变更、表单提交和重置事件。
4. 1. **数据验证**：支持Element Plus的表单验证规则，同时可以自动为必填字段生成验证规则。
5. 1. **响应式设计**：利用Vue3的reactive和computed API实现高效的响应式更新。

使用这个组件非常简单：

```jsx
// 使用示例
const formSchema = {
  fields: [
    {
      type: 'input',
      prop: 'projectName',
      label: '项目名称',
      required: true,
      maxlength: 50,
      showWordLimit: true
    },
    {
      type: 'select',
      prop: 'department',
      label: '所属部门',
      required: true,
      options: [
        { label: '技术部', value: 'tech' },
        { label: '财务部', value: 'finance' },
        { label: '人力资源部', value: 'hr' }
      ]
    },
    {
      type: 'date',
      prop: 'expectedStartDate',
      label: '预计开始日期',
      required: true
    }
  ]
}
```

## **为什么要开发可定制化组件库**

在招商信用卡中心POC项目管理平台开发过程中，我决定设计和开发这套可定制化组件库，主要基于以下几个关键原因：

**1. 业务场景的多样性和复杂性**

招商信用卡中心的审批流程非常复杂，不同部门、不同项目类型需要填写的表单字段各不相同。传统的硬编码表单难以应对这种变化：如果不使用可配置组件，每增加一种项目类型或修改字段，都需要修改代码并重新部署，这在快速迭代的环境中效率极低。

```jsx
// 不同项目类型需要不同表单配置
const projectFormConfigs = {
  'IT': [
    { field: 'projectName', label: '项目名称', type: 'input', required: true },
    { field: 'budget', label: '预算', type: 'number', required: true },
    { field: 'technicalRequirements', label: '技术需求', type: 'textarea', required: true },
    { field: 'vendorType', label: '厂商类型', type: 'select', options: vendorOptions }
  ],
  'Marketing': [
    { field: 'projectName', label: '项目名称', type: 'input', required: true },
    { field: 'budget', label: '预算', type: 'number', required: true },
    { field: 'targetAudience', label: '目标受众', type: 'select', options: audienceOptions },
    { field: 'marketingChannel', label: '营销渠道', type: 'checkbox', options: channelOptions }
  ]
  // 更多项目类型...
}
```

**2. 提高团队开发效率**

通过可复用组件库，我们将常用UI模式标准化：

项目组成员不再需要关心表单验证、状态管理、数据提交等基础逻辑，只需专注于业务规则实现。一位新加入的前端工程师在使用我的组件库后，开发效率提高了约40%。

```jsx
// 组件使用示例 - 只需要简单配置即可生成复杂表单
<template>
  <dynamic-form
    :schema="formSchema"
    :initial-values="initialValues"
    @submit="handleSubmit"
  />
</template>

<script>
export default {
  setup() {
    // 表单配置可以从后端动态获取
    const formSchema = ref({
      fields: [
        // 配置字段...
      ]
    })
    
    return { formSchema }
  }
}
</script>
```

**3. 确保产品一致性**

银行系统对UI一致性和用户体验有极高要求。我们通过组件库实现了整个系统的风格统一：

各个业务模块使用同一套组件，确保了按钮位置、交互方式、提示信息等完全一致，大大降低了用户的学习成本。

```jsx
// 组件内部处理了所有样式一致性问题
const tableConfig = {
  rowKey: 'id',
  columns: [
    { title: '项目名称', dataIndex: 'name', sortable: true },
    { title: '申请部门', dataIndex: 'department' },
    { title: '状态', dataIndex: 'status', render: 'statusTag' }, // 预定义渲染器
    { title: '操作', width: 180, fixed: 'right', render: 'actionButtons' }
  ],
  pagination: { pageSize: 10 },
  // 其他配置...
}
```

**5. 技术债务管理**

在项目早期，我发现团队成员编写了大量结构相似但细节不同的组件，如多个类似的表单和表格组件。这些重复代码难以维护：

开发组件库后，我们删除了约5000行重复代码，使代码库更加精简和可维护。

```jsx
// 项目早期 - 大量相似但略有不同的组件
ProjectForm.vue
VendorForm.vue 
UserForm.vue
// 重构后 - 一个组件解决所有问题
DynamicForm.vue
```



这个项目主要是给上海市民查询法规的

前台主要的就是查询搜索功能，这个搜索模块我是做成了一个通用的组件，因为搜索功能是在多页面都有的并且是和多个条件相关联的。还有版本切换，智能问答，数据埋点等功能，

后台的功能就更复杂了，主要的功能就是用户的登录注册，权限管理，**vue-tinymac** 发布修改法规，    **Echarts**数据可视化。然后后台这些功能也写了一个小程序端的使用的技术是uniapp

登录注册的实现，登录的话就是前端用双向绑定 (v-model)，实现输入框的数据绑定，确保用户输入的内容实时同步到组件的 `data`，还有就是前端的用户名密码校验之类的，再就是点击事件绑定

在提交的时候会有一个加密的过程，使用axios 请求接口，在axios 实例的拦截器里面的request.use、response.use统一进行数据的加密和解密，

我和后端约定好加密的key前端把整体加密的参数发送给后端，后端使用相同的key进行解密，接口返回code码提示登录成功获取到用户数据比如token之类的，前端需要进行保存在全局的vuex状态管理，还有一份要存在`localStorage`，在就进行路由的跳转，后面如果用户要调接口那就需要在请求头里面带上Token

难点的话，就是数据加密方面的，因为其实前端加密不是每家公司都做的，我在做小程序端的时候发现，小程序端的加密一般都是用的小程序原生的加密方式，而我在pc端写的是用cryptojs，这就会导致一个问题，小程序端没有这个crypto，我找了很多文档，各种论坛github找有人说可以直接把pc端的源码放到小程序里面，我发现导入导出会出错，不管是ES Modules、还是CommonJS的方式我都试了不行。那不加密去请求肯定也不行，所以我就和后端沟通，前端单独为小程序写一套加密解密的方法，通过platform参数来区分平台，如果是web那就用原来的，如果是miniapp，那就使用我这套加密方案，就是一套简单的凯撒密码加盐加密解密