# 前端面试题与答案

## 目录
- [JavaScript](#javascript)
- [TypeScript](#typescript)
- [浏览器底层原理](#浏览器底层原理)
- [网络协议](#网络协议)
- [Vue2/3](#vue23)
- [React](#react)
- [Uniapp和跨端开发](#uniapp和跨端开发)
- [前端性能优化](#前端性能优化)
- [构建工具和工程化](#构建工具和工程化)
- [Node.js](#nodejs)

## TypeScript

### 1. TypeScript与JavaScript的区别是什么？

**答案**：
TypeScript是JavaScript的超集，它扩展了JavaScript的语法，增加了静态类型系统和其他面向对象的特性。

**主要区别**：

1. **类型系统**：
   - JavaScript是动态弱类型语言，变量类型在运行时确定
   - TypeScript是静态强类型语言，变量类型在编译时确定

2. **编译过程**：
   - JavaScript可以直接在浏览器中执行
   - TypeScript需要先编译成JavaScript才能执行

3. **错误检测**：
   - TypeScript在编译阶段就能发现类型错误
   - JavaScript只能在运行时发现错误

4. **面向对象特性**：
   - TypeScript提供了更完整的面向对象编程支持，如接口、泛型、枚举等
   - JavaScript在ES6后增加了类，但面向对象支持不如TypeScript完善

5. **工具支持**：
   - TypeScript有更好的IDE支持，提供代码补全和智能提示
   - TypeScript的类型系统使重构更安全

**代码示例**：
```typescript
// JavaScript
function add(a, b) {
  return a + b;  // 可能导致字符串拼接而非数字相加
}

// TypeScript
function add(a: number, b: number): number {
  return a + b;  // 确保只接受数字类型参数
}
```

**考察重点**：
- 对TypeScript基本理念的理解
- TypeScript与JavaScript的关系
- TypeScript带来的优势

### 2. 详细解释TypeScript中的泛型及其应用

**答案**：
泛型是TypeScript中一种创建可重用组件的工具，它允许组件支持多种类型，而不必预先指定具体类型。

**泛型的作用**：
- 让函数、类或接口可以处理多种类型的数据
- 保持类型安全，避免使用any类型导致类型检查失效
- 提高代码复用性和灵活性

**基本用法**：

1. **泛型函数**：
```typescript
// 泛型函数
function identity<T>(arg: T): T {
  return arg;
}

// 调用方式
let output1 = identity<string>("myString");  // 显式指定类型
let output2 = identity("myString");  // 类型推断
```

2. **泛型接口**：
```typescript
interface GenericIdentityFn<T> {
  (arg: T): T;
}

let myIdentity: GenericIdentityFn<number> = identity;
```

3. **泛型类**：
```typescript
class GenericNumber<T> {
  zeroValue: T;
  add: (x: T, y: T) => T;
}

let myGenericNumber = new GenericNumber<number>();
myGenericNumber.zeroValue = 0;
myGenericNumber.add = function(x, y) { return x + y; };
```

4. **泛型约束**：限制泛型类型必须具有某些特性
```typescript
interface Lengthwise {
  length: number;
}

function loggingIdentity<T extends Lengthwise>(arg: T): T {
  console.log(arg.length);  // 可以确保arg有length属性
  return arg;
}
```

5. **多泛型参数**：
```typescript
function pair<T, U>(first: T, second: U): [T, U] {
  return [first, second];
}

let p = pair<string, number>("hello", 42);  // [string, number]
```

**实际应用**：
- 集合类（如数组、映射、队列等）
- API响应处理
- 状态管理库
- 高阶组件

**考察重点**：
- 泛型的基本概念和语法
- 泛型在不同场景下的应用
- 泛型约束的使用

### 3. TypeScript中interface和type有什么区别？

**答案**：
interface和type都可以用于定义类型，但它们有一些关键区别。

**共同点**：
- 都可以描述对象的形状
- 都支持扩展其他类型
- 都可以被类实现

**区别**：

1. **语法**：
```typescript
// Interface语法
interface Person {
  name: string;
  age: number;
}

// Type语法
type Person = {
  name: string;
  age: number;
};
```

2. **扩展方式**：
   - interface使用extends关键字扩展
   - type使用交叉类型(&)扩展
```typescript
// Interface扩展
interface Animal {
  name: string;
}
interface Dog extends Animal {
  bark(): void;
}

// Type扩展
type Animal = {
  name: string;
}
type Dog = Animal & {
  bark(): void;
}
```

3. **合并声明**：
   - interface可以多次声明，自动合并
   - type只能声明一次
```typescript
// Interface合并
interface User {
  name: string;
}
interface User {
  age: number;
}
// 等效于
interface User {
  name: string;
  age: number;
}

// Type不能重复声明
type User = { name: string };
// Error: Duplicate identifier 'User'
// type User = { age: number };
```

4. **类型表达能力**：
   - type可以表示联合类型、交叉类型、元组、映射类型等
   - interface表达能力相对受限
```typescript
// 只能使用type的情况
type ID = string | number;  // 联合类型
type Point = [number, number];  // 元组
type Readonly<T> = { readonly [P in keyof T]: T[P] };  // 映射类型
```

5. **计算属性**：
   - type支持使用in关键字进行映射
   - interface不支持
```typescript
type Keys = 'firstName' | 'lastName';
type DudeType = {
  [key in Keys]: string;  // 映射类型
};
// interface不支持类似语法
```

**选择建议**：
- 当需要声明合并或需要被类实现时，优先使用interface
- 当需要表达复杂类型或使用映射类型时，使用type
- 在库开发中，对外暴露API优先使用interface，便于扩展

**考察重点**：
- 对TypeScript类型系统的深入理解
- interface和type各自的特性和适用场景
- 在实际开发中的选择能力

### 4. 请解释TypeScript中的装饰器及其应用场景

**答案**：
装饰器是一种特殊类型的声明，可以附加到类、方法、访问器、属性或参数上，用于修改类的行为或添加元数据。

**装饰器类型**：

1. **类装饰器**：应用于类构造函数，可以修改或替换类定义
```typescript
function sealed(constructor: Function) {
  Object.seal(constructor);
  Object.seal(constructor.prototype);
}

@sealed
class Greeter {
  greeting: string;
  constructor(message: string) {
    this.greeting = message;
  }
  greet() {
    return "Hello, " + this.greeting;
  }
}
```

2. **方法装饰器**：应用于方法的属性描述符，可以修改方法的定义
```typescript
function log(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;
  
  descriptor.value = function(...args: any[]) {
    console.log(`Calling ${propertyKey} with args: ${JSON.stringify(args)}`);
    return originalMethod.apply(this, args);
  };
  
  return descriptor;
}

class Calculator {
  @log
  add(a: number, b: number) {
    return a + b;
  }
}

const calc = new Calculator();
calc.add(1, 2);  // 输出: "Calling add with args: [1,2]"
```

3. **访问器装饰器**：应用于访问器的属性描述符
```typescript
function configurable(value: boolean) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    descriptor.configurable = value;
  };
}

class Point {
  private _x: number;
  private _y: number;
  
  constructor(x: number, y: number) {
    this._x = x;
    this._y = y;
  }
  
  @configurable(false)
  get x() { return this._x; }
}
```

4. **属性装饰器**：应用于类的属性
```typescript
function format(formatString: string) {
  return function(target: any, propertyKey: string) {
    let value = target[propertyKey];
    
    const getter = function() {
      return value;
    };
    
    const setter = function(newVal: string) {
      value = formatString.replace("%s", newVal);
    };
    
    Object.defineProperty(target, propertyKey, {
      get: getter,
      set: setter,
      enumerable: true,
      configurable: true
    });
  };
}

class Greeter {
  @format("Hello, %s")
  greeting: string;
}
```

5. **参数装饰器**：应用于方法参数
```typescript
function required(target: Object, propertyKey: string, parameterIndex: number) {
  const metadataKey = `required_${propertyKey}_params`;
  
  if(Array.isArray(target[metadataKey])) {
    target[metadataKey].push(parameterIndex);
  }
  else {
    target[metadataKey] = [parameterIndex];
  }
}

class UserService {
  getUser(@required id: string) {
    // 实现...
  }
}
```

**应用场景**：
- 切面编程(AOP)：日志记录、性能监测、错误处理
- 依赖注入：类似Angular中的服务注入
- 验证：参数验证、表单验证
- 缓存：方法结果缓存
- 权限控制：方法访问权限检查

**考察重点**：
- 装饰器的基本原理和类型
- 装饰器工厂的实现
- 在实际开发中的应用场景

### 5. TypeScript中高级类型的使用(联合类型、交叉类型、条件类型等)

**答案**：
TypeScript提供了多种高级类型机制，使类型系统更加灵活和强大。

**主要高级类型**：

1. **联合类型(Union Types)**：表示一个值可以是几种类型之一
```typescript
// 可以是字符串或数字
let id: string | number;
id = "abc123";  // 有效
id = 123;       // 有效
id = true;      // 错误
```

2. **交叉类型(Intersection Types)**：将多个类型合并为一个类型
```typescript
interface Person {
  name: string;
  age: number;
}

interface Employee {
  companyId: string;
  role: string;
}

type EmployeePerson = Person & Employee;

// 必须同时满足两个接口的所有属性
const ep: EmployeePerson = {
  name: "John",
  age: 30,
  companyId: "E123",
  role: "Developer"
};
```

3. **类型别名(Type Aliases)**：为类型创建新名称
```typescript
type ID = string | number;
type UserCallback = (user: User) => void;
```

4. **字面量类型(Literal Types)**：指定一个值的具体类型
```typescript
// 只能是这三个具体的字符串之一
type Direction = "north" | "south" | "east" | "west";

function move(direction: Direction) {
  // ...
}

move("north");  // 有效
move("up");     // 错误
```

5. **索引类型(Index Types)**：
```typescript
// 使用keyof获取对象的键
function getProperty<T, K extends keyof T>(obj: T, key: K): T[K] {
  return obj[key];
}

const user = { name: "John", age: 30 };
const name = getProperty(user, "name");  // 返回 "John"
const invalid = getProperty(user, "email");  // 错误: "email"不是"name"|"age"
```

6. **映射类型(Mapped Types)**：基于旧类型创建新类型
```typescript
// 将所有属性设为只读
type Readonly<T> = {
  readonly [P in keyof T]: T[P];
};

// 将所有属性设为可选
type Partial<T> = {
  [P in keyof T]?: T[P];
};

interface User {
  name: string;
  age: number;
}

const readonlyUser: Readonly<User> = {
  name: "John",
  age: 30
};
// readonlyUser.name = "Jane";  // 错误：name是只读属性
```

7. **条件类型(Conditional Types)**：基于条件表达式的类型
```typescript
type NonNullable<T> = T extends null | undefined ? never : T;

// 提取函数返回类型
type ReturnType<T> = T extends (...args: any[]) => infer R ? R : any;

function greeting() {
  return "Hello, World";
}

type GreetingReturn = ReturnType<typeof greeting>;  // string
```

8. **工具类型(Utility Types)**：TypeScript内置的类型转换工具
```typescript
// Partial: 使所有属性变为可选
interface User {
  name: string;
  age: number;
}
const partialUser: Partial<User> = { name: "John" };  // age可省略

// Pick: 从类型中选择部分属性
type NameOnly = Pick<User, "name">;  // { name: string }

// Omit: 排除某些属性
type WithoutAge = Omit<User, "age">;  // { name: string }

// Record: 创建键类型到值类型的映射
type UserRoles = Record<string, string>;
/*
{
  [key: string]: string
}
*/
```

**考察重点**：
- 各种高级类型的用法和适用场景
- 组合使用高级类型解决复杂问题
- 理解TypeScript类型系统的灵活性和表达能力

## 网络协议

### 1. HTTP/1.0、HTTP/1.1和HTTP/2.0的主要区别是什么？

**答案**：
HTTP协议的不同版本在性能、功能和安全性方面有显著差异。

**HTTP/1.0与HTTP/1.1的区别**：

1. **连接方式**：
   - HTTP/1.0默认使用非持久连接，每次请求都需要建立新的TCP连接
   - HTTP/1.1默认使用持久连接(keep-alive)，多个请求可以复用同一个TCP连接

2. **域名分片**：
   - HTTP/1.1支持同一域名下并行发送多个请求，但仍受到浏览器并发连接数限制
   - 为解决这个问题，出现了域名分片技术(Domain Sharding)，将资源分散到多个子域名

3. **缓存处理**：
   - HTTP/1.0使用Expires和Last-Modified等有限的缓存控制
   - HTTP/1.1引入了更强大的缓存控制机制，如Cache-Control和ETag

4. **带宽优化**：
   - HTTP/1.1引入了范围请求(Range Requests)，允许只请求资源的一部分
   - 支持压缩内容编码(Content-Encoding)

5. **Host头**：
   - HTTP/1.1要求请求报文中必须包含Host头部，允许在同一IP地址上托管多个域名

**HTTP/1.1与HTTP/2.0的区别**：

1. **多路复用(Multiplexing)**：
   - HTTP/1.1每个请求都会阻塞后面的请求，导致队头阻塞(Head-of-Line Blocking)
   - HTTP/2.0引入多路复用，允许在同一连接上并行处理多个请求/响应，不会互相阻塞

2. **二进制协议**：
   - HTTP/1.1是文本协议，解析较慢且容易出错
   - HTTP/2.0是二进制协议，将请求和响应划分为更小的帧，更高效也更安全

3. **头部压缩**：
   - HTTP/1.1的头部未经压缩，每次请求都会携带完整头部
   - HTTP/2.0使用HPACK算法压缩头部，减少冗余数据传输

4. **服务器推送**：
   - HTTP/2.0支持服务器推送(Server Push)，服务器可以主动向客户端推送资源，无需等待客户端请求

5. **流优先级**：
   - HTTP/2.0允许为请求设置优先级，优化关键资源的加载

**HTTP/2.0与HTTP/3.0(QUIC)的主要区别**：

1. **传输协议**：
   - HTTP/2.0基于TCP协议
   - HTTP/3.0基于UDP协议的QUIC协议

2. **连接建立**：
   - HTTP/2.0需要TCP三次握手建立连接
   - HTTP/3.0基于QUIC，首次连接只需1-RTT，后续连接可能实现0-RTT

3. **队头阻塞**：
   - HTTP/2.0解决了HTTP层面的队头阻塞，但TCP层面仍存在队头阻塞
   - HTTP/3.0通过独立的QUIC流，彻底解决了队头阻塞问题

**代码示例(HTTP请求头对比)**：

```http
// HTTP/1.0请求
GET /index.html HTTP/1.0
User-Agent: Mozilla/5.0
Accept: text/html

// HTTP/1.1请求
GET /index.html HTTP/1.1
Host: www.example.com
User-Agent: Mozilla/5.0
Accept: text/html
Connection: keep-alive

// HTTP/2.0请求(概念表示，实际上是二进制)
:method: GET
:path: /index.html
:authority: www.example.com
:scheme: https
user-agent: Mozilla/5.0
accept: text/html
```

**考察重点**：
- 不同HTTP版本的特性和优化点
- HTTP协议演进对Web性能的影响
- 对现代HTTP版本的实际应用理解

### 2. 详细解释TCP的三次握手和四次挥手过程

**答案**：
TCP(传输控制协议)是一种面向连接的、可靠的、基于字节流的传输层通信协议。它的连接建立和断开过程分别为三次握手和四次挥手。

**TCP三次握手过程**：

1. **第一次握手 (SYN)**：
   - 客户端发送SYN包(seq=x)到服务器，并进入SYN_SENT状态
   - 这一步是客户端告诉服务器："我想建立连接，我的初始序列号是x"

2. **第二次握手 (SYN+ACK)**：
   - 服务器收到SYN包，回应一个SYN+ACK包(seq=y, ACK=x+1)
   - 服务器进入SYN_RECV状态
   - 这一步是服务器告诉客户端："我已收到你的请求，我的初始序列号是y，我已确认你的序列号x"

3. **第三次握手 (ACK)**：
   - 客户端收到SYN+ACK包，回应一个ACK包(ACK=y+1)
   - 客户端和服务器都进入ESTABLISHED状态，连接建立完成
   - 这一步是客户端告诉服务器："我已确认你的序列号y"

**为什么需要三次握手**：
- 确保双方都有接收和发送能力
- 同步双方的初始序列号(ISN)
- 防止历史连接的错误建立（如果只有两次握手，服务器无法确认客户端是否收到了自己的确认）

**TCP四次挥手过程**：

1. **第一次挥手 (FIN)**：
   - 客户端发送FIN包(seq=u)，表示客户端没有数据要发送了
   - 客户端进入FIN_WAIT_1状态
   - 此时客户端只关闭了数据发送通道，仍可接收服务器数据

2. **第二次挥手 (ACK)**：
   - 服务器收到FIN包，回应ACK包(ACK=u+1)
   - 服务器进入CLOSE_WAIT状态，客户端收到后进入FIN_WAIT_2状态
   - 此时表示服务器确认了客户端的关闭请求，但服务器可能还有数据要发送

3. **第三次挥手 (FIN)**：
   - 服务器发送完所有数据后，发送FIN包(seq=v)
   - 服务器进入LAST_ACK状态
   - 此时服务器告诉客户端："我也没有数据要发送了"

4. **第四次挥手 (ACK)**：
   - 客户端收到FIN包，回应ACK包(ACK=v+1)
   - 客户端进入TIME_WAIT状态，等待2MSL(最大报文生存时间)后关闭
   - 服务器收到ACK后关闭连接

**为什么需要四次挥手**：
- TCP连接是全双工的，需要单独关闭两个方向的连接
- 服务器收到客户端的关闭请求后，可能还有数据需要发送，所以ACK和FIN是分开发送的

**TIME_WAIT状态的作用**：
- 确保最后一个ACK能到达服务器（如果丢失，服务器会重发FIN，客户端可以重发ACK）
- 防止延迟的旧报文段被后续连接接收处理

**代码示例**：
```javascript
// 使用Node.js的net模块创建TCP服务器和客户端
// 服务器端
const net = require('net');
const server = net.createServer((socket) => {
  console.log('客户端已连接');
  
  socket.on('data', (data) => {
    console.log(`接收到数据: ${data}`);
    socket.write('服务器已收到数据');
  });
  
  socket.on('end', () => {
    console.log('客户端已断开连接');
  });
});

server.listen(8080, () => {
  console.log('服务器已启动');
});

// 客户端
const client = net.createConnection({ port: 8080 }, () => {
  console.log('已连接到服务器');
  client.write('你好，服务器');
});

client.on('data', (data) => {
  console.log(`服务器回应: ${data}`);
  client.end(); // 开始断开连接
});

client.on('end', () => {
  console.log('已断开与服务器的连接');
});
```

**考察重点**：
- TCP连接建立和断开的详细过程
- 每次握手和挥手的状态转换
- 理解TCP可靠性设计的原理

## 浏览器底层原理

### 1. 详细描述浏览器的渲染过程

**答案**：
浏览器的渲染过程是页面从HTML、CSS和JavaScript转变为用户可见内容的过程，主要包含以下步骤：

**1. 解析HTML构建DOM树**：
- 浏览器从网络或本地获取HTML文本
- HTML解析器解析HTML标签，构建DOM(Document Object Model)树
- DOM树是文档的对象表示，每个HTML元素都对应一个DOM节点

**2. 解析CSS构建CSSOM树**：
- 浏览器解析外部CSS文件、内部样式表、行内样式
- 生成CSSOM(CSS Object Model)树，包含所有样式规则
- CSSOM树结构与DOM树类似，但只包含样式信息

**3. 合并DOM和CSSOM构建渲染树**：
- 从DOM树的根节点开始，遍历每个可见节点
- 对于每个可见节点，查找CSSOM中适用的样式规则并应用
- 生成渲染树(Render Tree)，包含所有可见元素及其样式信息
- 注意：不可见元素（如`<head>`、`display: none`的元素）不会出现在渲染树中

**4. 布局(Layout/Reflow)**：
- 计算每个节点在屏幕上的精确坐标和大小
- 从渲染树的根节点开始，递归地确定每个元素的盒模型尺寸和位置
- 布局受到视口大小、元素盒模型属性(margin、border、padding等)的影响
- 输出包含所有节点精确位置和尺寸的布局树(Layout Tree)

**5. 绘制(Paint)**：
- 将布局树转换为屏幕上的实际像素
- 绘制过程分多个图层进行，如背景、文字、边框等
- 涉及填充像素颜色的过程，将每个节点转换为实际的图像
- 生成图层树(Layer Tree)

**6. 合成(Compositing)**：
- 将不同的图层按照正确的顺序合成到屏幕上
- 考虑元素的z-index和透明度等因素
- 某些元素可能提升为单独的图层(例如使用transform、opacity属性的元素)
- GPU加速可能应用于此过程

**渲染优化关键点**：

1. **渲染阻塞资源**：
   - CSS被视为渲染阻塞资源，浏览器会等待CSSOM构建完成再进行渲染
   - 可以使用媒体查询使CSS非阻塞：`<link rel="stylesheet" href="style.css" media="print">`

2. **JavaScript执行**：
   - JavaScript可以修改DOM和CSSOM，因此会阻塞渲染
   - 可以使用`async`或`defer`属性优化脚本加载：
   ```html
   <script src="script.js" async></script>
   <script src="script.js" defer></script>
   ```

3. **关键渲染路径优化**：
   - 减少关键资源数量(CSS、JavaScript等)
   - 减少关键资源大小(压缩、代码分割)
   - 优化加载顺序(预加载关键资源)

**浏览器渲染相关API**：
```javascript
// 使用requestAnimationFrame优化动画，在下一次重绘前执行
function animate() {
  // 更新元素样式
  element.style.transform = `translateX(${position}px)`;
  
  // 安排下一帧
  requestAnimationFrame(animate);
}
requestAnimationFrame(animate);

// 避免强制同步布局
// 不好的写法：读取后立即写入，导致强制同步布局
function badLayout() {
  const width = element.offsetWidth; // 读取
  element.style.width = (width + 10) + 'px'; // 写入
  const height = element.offsetHeight; // 又读取，触发强制同步布局
}

// 好的写法：批量读取，然后批量写入
function goodLayout() {
  const width = element.offsetWidth; // 读取
  const height = element.offsetHeight; // 读取
  
  element.style.width = (width + 10) + 'px'; // 写入
  element.style.height = (height + 10) + 'px'; // 写入
}
```

**考察重点**：
- 浏览器渲染的完整流程
- DOM、CSSOM、渲染树的概念和关系
- 布局和绘制过程的理解
- 渲染性能优化的关键点

### 2. 什么是重排（回流）和重绘？它们的区别是什么？如何避免？

**答案**：
重排（回流）和重绘是浏览器渲染过程中的两个关键步骤，它们会在页面初始渲染后因为DOM操作、样式更改等原因被重新触发，影响性能。

**重排(Reflow/Layout)定义**：
- 重排是浏览器重新计算元素位置和几何信息的过程
- 当页面布局和几何属性改变时触发
- 重排是一个性能消耗较大的操作

**重绘(Repaint)定义**：
- 重绘是浏览器重新绘制元素外观的过程，如颜色、背景等
- 不改变布局，只改变元素外观时触发
- 重绘的性能消耗通常小于重排

**区别**：
1. **触发原因不同**：
   - 重排：元素的位置、大小、结构等发生变化
   - 重绘：元素外观改变但不影响布局，如颜色、背景等

2. **影响范围不同**：
   - 重排可能影响整个文档，因为一个元素的大小变化可能引起其他元素的位置变化
   - 重绘通常只影响特定元素

3. **性能消耗**：
   - 重排的性能消耗大，因为需要重新计算布局
   - 重绘的性能消耗相对较小

4. **触发关系**：
   - 重排必定会触发重绘，但重绘不一定触发重排
   - 如修改宽高会导致重排，然后触发重绘；修改颜色只会导致重绘

**触发重排的操作**：
- DOM元素的添加、删除、移动
- 调整窗口大小、字体大小
- 内容变化
- 计算或访问某些属性：
  - `offsetTop`, `offsetLeft`, `offsetWidth`, `offsetHeight`
  - `clientTop`, `clientLeft`, `clientWidth`, `clientHeight`
  - `scrollTop`, `scrollLeft`, `scrollWidth`, `scrollHeight`
  - `getComputedStyle()`
  - `getBoundingClientRect()`

**触发重绘的操作**：
- 修改颜色
- 修改背景图像
- 修改阴影
- 修改可见性（visibility）
- 其它不影响布局的样式变化

**避免重排和重绘的优化策略**：

1. **批量修改DOM**：
```javascript
// 不好的做法：多次直接修改DOM
const element = document.getElementById('myElement');
element.style.width = '100px';
element.style.height = '100px';
element.style.marginTop = '20px';

// 好的做法1：使用类名批量修改
element.className = 'newStyle';

// 好的做法2：修改cssText
element.style.cssText = 'width: 100px; height: 100px; margin-top: 20px;';

// 好的做法3：使用documentFragment
const fragment = document.createDocumentFragment();
for (let i = 0; i < 10; i++) {
  const child = document.createElement('div');
  child.textContent = `Item ${i}`;
  fragment.appendChild(child);
}
document.getElementById('container').appendChild(fragment);
```

2. **使用变换代替位置修改**：
```javascript
// 不好的做法：修改元素位置，触发重排
element.style.left = '100px';
element.style.top = '100px';

// 好的做法：使用transform，只触发合成
element.style.transform = 'translate(100px, 100px)';
```

3. **避免频繁访问会引起重排的属性**：
```javascript
// 不好的做法：多次访问引起强制同步重排的属性
for (let i = 0; i < 100; i++) {
  console.log(element.offsetWidth); // 每次访问都可能触发重排
}

// 好的做法：缓存属性值
const width = element.offsetWidth; // 只触发一次重排
for (let i = 0; i < 100; i++) {
  console.log(width);
}
```

4. **对于动画和频繁变化，使用绝对定位脱离文档流**：
```css
.animated-element {
  position: absolute;
  /* 或 position: fixed; */
  /* 此元素的变化不会影响其他元素的布局 */
}
```

5. **使用CSS3硬件加速**：
```css
.hardware-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
  /* 使用GPU加速，减少重排和重绘 */
}
```

6. **使用requestAnimationFrame管理动画**：
```javascript
function animate() {
  // 修改元素样式
  element.style.transform = `translateX(${position}px)`;
  
  // 下一帧再执行
  requestAnimationFrame(animate);
}
requestAnimationFrame(animate);
```

**考察重点**：
- 重排和重绘的概念和区别
- 触发重排和重绘的不同操作
- 如何通过编码方式减少重排和重绘
- 理解重排和重绘对性能的影响

## Vue2/3

### 1. Vue2和Vue3的主要区别有哪些？

**答案**：
Vue2和Vue3的主要区别包括：

**1. 核心架构**：
- Vue3采用了全新的Composition API，允许更灵活的代码组织和逻辑重用
- Vue2主要使用Options API，通过预定义的选项组织代码

**2. 性能提升**：
- Vue3使用Proxy代替Vue2的Object.defineProperty进行响应式追踪，解决了Vue2的数组和对象响应式限制
- Vue3引入了基于Proxy的响应式系统，可以监听对象属性的添加和删除，以及数组索引和length的变化
- Vue3的虚拟DOM重写，采用了静态树提升、静态属性提升等优化
- Vue3的编译器优化，通过块树(Block Tree)实现更精确的DOM更新
- Vue3支持Tree-shaking，只打包用到的功能，减小包体积

**3. 语法和API变化**：
- Vue3支持多根节点组件（片段），不再要求组件必须有单一根节点
- Vue3的v-model支持多个绑定和自定义修饰符
- Vue3的异步组件需要通过defineAsyncComponent函数定义
- Vue3的组件事件现在作为组件实例的一部分（emit被定义为组件实例的方法）

**4. TypeScript支持**：
- Vue3是用TypeScript重写的，提供了更好的类型推导
- Vue3组件的props和事件有更好的类型检查

**5. 组件通信**：
- Vue3移除了$on、$off、$once方法，事件总线需要使用第三方库或实现
- Vue3推荐使用provide/inject进行深层组件通信

**6. 生命周期钩子变化**：
- beforeCreate 和 created 被 setup 函数替代
- beforeDestroy 和 destroyed 重命名为 beforeUnmount 和 unmounted
- Composition API中的生命周期钩子使用特定函数，如onMounted、onUpdated等

**7. 工具链和IDE支持**：
- Vue3的编译器可以生成更多编译时提示，帮助开发者定位模板错误
- Volar取代Vetur成为主要的Vue3 IDE插件，提供更好的TypeScript支持

**8. 全局API变化**：
- Vue3废弃了过滤器(filter)
- Vue3中的全局API移至应用实例（app），如app.component()、app.directive()等
- Vue3创建应用实例的方式变为：createApp(component)

**9. 组合式API（Composition API）**：
- 提供setup函数作为组件的入口点
- 引入ref、reactive等响应式API
- 提供生命周期钩子、依赖注入、模板引用等功能
- 支持逻辑提取和复用，解决了mixins的一些问题

### 2. 解释Vue的响应式原理

**答案**：
Vue的响应式原理是其核心特性之一，Vue2和Vue3在实现上有所不同：

**Vue2的响应式原理**：

1. **数据劫持**：
   - 使用Object.defineProperty()对对象的每个属性进行劫持
   - 为每个属性定义getter和setter
   - getter用于依赖收集，setter用于触发更新

2. **依赖收集**：
   - 当组件渲染时，会访问数据的getter
   - 每个组件实例对应一个watcher实例
   - 在getter中收集当前正在计算的watcher作为依赖

3. **派发更新**：
   - 当数据变化时，setter被调用
   - setter通知所有依赖该数据的watcher
   - watcher触发组件重新渲染

4. **局限性**：
   - 不能检测对象属性的添加和删除（需要使用Vue.set/Vue.delete）
   - 不能直接检测数组索引的变化和length的修改
   - 需要对深层对象递归进行劫持，初始化性能消耗大

**Vue3的响应式原理**：

1. **Proxy代理**：
   - 使用ES6的Proxy替代Object.defineProperty
   - 代理整个对象而不是单个属性，可以拦截多种操作
   - 能够检测对象属性的添加和删除
   - 能够检测数组索引和length的变化

2. **依赖收集**：
   - 基于effect函数实现
   - 使用WeakMap、Map和Set存储依赖关系
   - 形成"对象->属性->effect"的树形结构

3. **派发更新**：
   - 当Proxy拦截到属性变化，查找并执行依赖该属性的所有effect
   - 支持嵌套effect，有更精确的依赖追踪

4. **优势**：
   - 性能更好，尤其是对于大型对象
   - 完全可以监听对象和数组的所有变化
   - 惰性观察，只有被访问过的属性才会被跟踪
   - 不需要Vue.set和Vue.delete这样的辅助API

### 3. Vue中的组件通信方式有哪些？

**答案**：
Vue提供了多种组件间通信方式，适用于不同的场景：

**1. Props（父传子）**：
```vue
<!-- 父组件 -->
<child-component :message="parentMessage"></child-component>

<!-- 子组件 -->
<script>
export default {
  props: ['message'] // 或者使用更详细的验证 props: { message: { type: String, required: true } }
}
</script>
```

**2. 事件（子传父）**：
```vue
<!-- 子组件 -->
<template>
  <button @click="sendMessage">发送消息</button>
</template>
<script>
export default {
  methods: {
    sendMessage() {
      this.$emit('message-sent', '子组件的消息');
    }
  }
}
</script>

<!-- 父组件 -->
<child-component @message-sent="handleMessage"></child-component>
<script>
export default {
  methods: {
    handleMessage(msg) {
      console.log(msg); // '子组件的消息'
    }
  }
}
</script>
```

**3. 依赖注入（跨多层组件通信）**：
```vue
<!-- 祖先组件 -->
<script>
export default {
  provide() {
    return {
      sharedData: this.sharedData
    }
  },
  data() {
    return {
      sharedData: '共享数据'
    }
  }
}
</script>

<!-- 后代组件 -->
<script>
export default {
  inject: ['sharedData']
}
</script>
```

**4. Vuex（全局状态管理）**：
```javascript
// store.js
import { createStore } from 'vuex'

export default createStore({
  state: {
    count: 0
  },
  mutations: {
    increment(state) {
      state.count++
    }
  }
})

// 组件中使用
import { mapState, mapMutations } from 'vuex'

export default {
  computed: {
    ...mapState(['count'])
  },
  methods: {
    ...mapMutations(['increment'])
  }
}
```

**5. 事件总线（Vue2中的全局事件通信）**：
```javascript
// Vue2
// 创建事件总线
Vue.prototype.$bus = new Vue();

// 发送事件
this.$bus.$emit('custom-event', data);

// 接收事件
this.$bus.$on('custom-event', this.handleEvent);
this.$bus.$off('custom-event', this.handleEvent); // 移除监听

// Vue3（需要单独实现或使用第三方库）
import mitt from 'mitt';
const emitter = mitt();

// 发送事件
emitter.emit('custom-event', data);

// 接收事件
emitter.on('custom-event', handleEvent);
emitter.off('custom-event', handleEvent); // 移除监听
```

**6. $refs（直接访问子组件）**：
```vue
<!-- 父组件 -->
<template>
  <child-component ref="childRef"></child-component>
  <button @click="callChildMethod">调用子组件方法</button>
</template>
<script>
export default {
  methods: {
    callChildMethod() {
      this.$refs.childRef.childMethod();
    }
  }
}
</script>
```

**7. Pinia（Vue3推荐的状态管理库）**：
```javascript
// store.js
import { defineStore } from 'pinia'

export const useCounterStore = defineStore('counter', {
  state: () => ({ count: 0 }),
  actions: {
    increment() {
      this.count++
    }
  }
})

// 组件中使用
import { useCounterStore } from '@/stores/counter'

export default {
  setup() {
    const counter = useCounterStore()
    return { counter }
  }
}
```

### 4. 说说Vue中的虚拟DOM及其Diff算法

**答案**：
虚拟DOM（Virtual DOM）是Vue中用JavaScript对象描述真实DOM结构的轻量级JavaScript对象树。

**虚拟DOM的工作原理**：

1. **创建阶段**：
   - 将模板编译成渲染函数
   - 渲染函数执行后返回虚拟DOM树
   - 虚拟DOM节点通常包含tag（标签名）、props（属性）、children（子节点）等信息

2. **更新阶段**：
   - 当数据变化时，重新执行渲染函数，生成新的虚拟DOM树
   - 通过Diff算法比较新旧虚拟DOM树的差异
   - 将差异应用到真实DOM上，只更新变化的部分

3. **优势**：
   - 减少直接操作DOM的次数，提高性能
   - 跨平台能力，可以渲染到不同平台（浏览器DOM、Native、Canvas等）
   - 批量更新DOM，减少浏览器重排和重绘
   - 开发者可以声明式地编写UI，而不必关心DOM操作细节

**Vue的Diff算法**：

1. **基本原理**：
   - 只比较同层级的节点，不跨层级比较
   - 采用"就地复用"策略，尽可能复用已有节点

2. **Vue2的Diff算法**：
   - 采用双端比较（double-ended comparison）算法
   - 四个指针分别指向新旧两个子节点列表的头尾
   - 每轮比较从四种可能的头尾匹配开始
   - 如果四种匹配都失败，则通过key进行节点复用

3. **Vue3的Diff算法改进**：
   - 增加静态节点标记，跳过静态内容的比较
   - 使用最长递增子序列算法优化列表对比
   - 引入Block概念，只对动态节点进行追踪和Diff

4. **Key的作用**：
   - 帮助Vue识别节点，在节点位置变化时重用节点
   - 没有key时Vue会尝试就地修改/复用节点
   - 有key时可以更准确地找到对应节点，减少不必要的DOM操作

### 5. Vue的生命周期钩子有哪些？在什么场景下使用？

**答案**：
Vue的生命周期钩子是指在组件不同阶段触发的函数，Vue2和Vue3的生命周期钩子有所不同：

**Vue2生命周期钩子**：

1. **beforeCreate**：
   - 触发时机：实例初始化后，数据观测和事件配置之前
   - 此时无法访问数据（data）和方法（methods）
   - 应用场景：初始化非响应式变量，引入第三方库等

2. **created**：
   - 触发时机：实例创建后，观察者、计算属性、方法和事件回调设置完成
   - 此时可以访问数据和方法，但DOM尚未生成
   - 应用场景：发送API请求、初始化数据、在服务器端渲染时进行数据准备

3. **beforeMount**：
   - 触发时机：模板编译完成，但还未挂载到DOM
   - 应用场景：需要访问DOM之前的最后一次准备工作

4. **mounted**：
   - 触发时机：组件挂载到DOM后
   - 此时可以访问$el（组件的DOM元素）和ref（组件引用）
   - 应用场景：需要操作DOM的初始化工作、启动第三方库、添加事件监听器、设置定时器等

5. **beforeUpdate**：
   - 触发时机：数据变化后，DOM更新之前
   - 应用场景：在DOM更新前访问现有DOM，例如手动移除已添加的事件监听器

6. **updated**：
   - 触发时机：DOM更新完成后
   - 应用场景：执行依赖于DOM更新的操作，注意避免在此钩子中修改数据，防止无限循环

7. **beforeDestroy（Vue3中重命名为beforeUnmount）**：
   - 触发时机：实例销毁前
   - 应用场景：清理工作，如移除事件监听器、取消订阅、清除定时器等

8. **destroyed（Vue3中重命名为unmounted）**：
   - 触发时机：实例销毁后
   - 应用场景：最终的清理工作，很少使用，因为大多数清理工作应在beforeDestroy完成

**额外的生命周期钩子**：

9. **activated**：
   - 触发时机：被keep-alive缓存的组件激活时
   - 应用场景：恢复组件状态，例如重新获取数据或重置定时器

10. **deactivated**：
    - 触发时机：被keep-alive缓存的组件停用时
    - 应用场景：暂停动画、保存状态、暂停定时器等

11. **errorCaptured**：
    - 触发时机：捕获到后代组件错误时
    - 应用场景：错误处理和日志记录

**Vue3 Composition API中的生命周期钩子**：

```javascript
import { 
  onBeforeMount, 
  onMounted, 
  onBeforeUpdate, 
  onUpdated, 
  onBeforeUnmount, 
  onUnmounted, 
  onActivated, 
  onDeactivated, 
  onErrorCaptured 
} from 'vue';

export default {
  setup() {
    // beforeCreate 和 created 的替代
    console.log('在 setup 中');
    
    onBeforeMount(() => {
      console.log('onBeforeMount');
    });
    
    onMounted(() => {
      console.log('onMounted');
    });
    
    onBeforeUpdate(() => {
      console.log('onBeforeUpdate');
    });
    
    onUpdated(() => {
      console.log('onUpdated');
    });
    
    onBeforeUnmount(() => {
      console.log('onBeforeUnmount');
    });
    
    onUnmounted(() => {
      console.log('onUnmounted');
    });
    
    onActivated(() => {
      console.log('onActivated');
    });
    
    onDeactivated(() => {
      console.log('onDeactivated');
    });
    
    onErrorCaptured((err, instance, info) => {
      console.log('onErrorCaptured');
      return false; // 阻止错误继续传播
    });
    
    // Vue3特有的钩子
    onRenderTracked((event) => {
      console.log('onRenderTracked');
    });
    
    onRenderTriggered((event) => {
      console.log('onRenderTriggered');
    });
  }
}
```

**生命周期钩子的应用场景**：

1. **初始化和数据获取**：
   - created：获取初始数据、设置初始状态
   - mounted：初始化需要DOM的插件、设置事件监听器

2. **数据监听和DOM更新**：
   - beforeUpdate：在DOM更新前保存状态或处理数据
   - updated：在DOM更新后执行DOM依赖的操作，例如更新第三方UI库

3. **性能优化**：
   - 使用keep-alive结合activated/deactivated缓存组件状态，避免重复渲染
   - 在beforeDestroy中清理定时器、取消API请求等，避免内存泄漏

4. **错误处理**：
   - errorCaptured：捕获和处理后代组件的错误

5. **组件通信**：
   - 在父组件的mounted中访问子组件方法
   - 在子组件mounted中通知父组件加载完成

6. **保持状态**：
   - 在beforeDestroy中保存状态，在created/mounted中恢复状态
```

## React

### 1. React中的类组件和函数组件有什么区别？

**答案**：
React的类组件和函数组件是两种定义组件的方式，它们在语法、特性和使用场景上有所不同。

**1. 语法差异**：
```jsx
// 类组件
class Welcome extends React.Component {
  render() {
    return <h1>Hello, {this.props.name}</h1>;
  }
}

// 函数组件
function Welcome(props) {
  return <h1>Hello, {props.name}</h1>;
}

// 函数组件（箭头函数写法）
const Welcome = (props) => <h1>Hello, {props.name}</h1>;
```

**2. 特性差异**：

- **状态管理**：
  - 类组件可以使用this.state和this.setState()管理内部状态
  - 函数组件在React 16.8引入Hooks之前没有状态，引入useState后可以管理状态

- **生命周期**：
  - 类组件有完整的生命周期方法(componentDidMount, componentDidUpdate等)
  - 函数组件通过useEffect hook模拟生命周期行为

- **this关键字**：
  - 类组件使用this访问props、state和自定义方法
  - 函数组件不使用this，直接访问props和通过闭包访问状态

- **性能**：
  - 函数组件相对更轻量，没有实例化过程
  - 类组件实例化后可以保持状态，但开销略大

**3. React Hooks**：
React 16.8引入Hooks后，函数组件能够实现之前只有类组件才能实现的功能：

```jsx
// 使用useState管理状态
import React, { useState, useEffect } from 'react';

function Counter() {
  // 声明状态变量count，初始值为0
  const [count, setCount] = useState(0);
  
  // 类似于componentDidMount和componentDidUpdate
  useEffect(() => {
    document.title = `You clicked ${count} times`;
  });
  
  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
```

**4. 何时使用类组件vs函数组件**：

- **类组件适用场景**：
  - 需要使用React 16.8之前的遗留代码
  - 需要使用错误边界(Error Boundaries)
  - 偏好面向对象编程风格

- **函数组件适用场景**：
  - 新的React项目（React团队推荐）
  - 简单的展示型组件
  - 注重代码可读性和测试性
  - 需要使用React新特性

**5. 最佳实践**：
- React团队建议新代码优先使用函数组件和Hooks
- 不需要重写现有的类组件
- 可以在同一个项目中混合使用两种组件类型

**考察重点**：
- 对React组件定义方式的理解
- 类组件和函数组件的优缺点
- Hooks的基本使用
- 组件选择的思考过程

### 2. React中的状态管理方式有哪些？Redux的工作原理是什么？

**答案**：
React应用中有多种状态管理方式，从简单到复杂各有适用场景，其中Redux是较为流行的状态管理库之一。

**React状态管理方式**：

1. **组件内部状态(Local State)**：
   - 使用类组件的this.state和this.setState()
   - 使用函数组件的useState Hook
   ```jsx
   function Counter() {
     const [count, setCount] = useState(0);
     return (
       <div>
         <p>Count: {count}</p>
         <button onClick={() => setCount(count + 1)}>Increment</button>
       </div>
     );
   }
   ```

2. **通过Props传递状态(Prop Drilling)**：
   - 将状态提升到共同的父组件，通过props向下传递
   - 简单应用中够用，但组件层级深时变得繁琐

3. **Context API**：
   - React内置API，用于避免多层级props传递
   - 适合中小型应用和特定领域的状态(如主题、用户信息)
   ```jsx
   // 创建上下文
   const ThemeContext = React.createContext('light');
   
   // 提供者
   function App() {
     return (
       <ThemeContext.Provider value="dark">
         <Toolbar />
       </ThemeContext.Provider>
     );
   }
   
   // 类组件消费Context
   class ThemedButton extends React.Component {
     static contextType = ThemeContext;
     render() {
       return <Button theme={this.context} />;
     }
   }
   
   // 函数组件使用useContext钩子
   function ThemedButton() {
     const theme = useContext(ThemeContext);
     return <Button theme={theme} />;
   }
   
   // 使用Consumer组件
   function ThemedButton() {
     return (
       <ThemeContext.Consumer>
         {theme => <Button theme={theme} />}
       </ThemeContext.Consumer>
     );
   }
   ```

4. **Redux**：
   - 完整的状态管理解决方案，基于单向数据流
   - 适合大型应用和复杂状态逻辑

5. **MobX**：
   - 基于可观察对象的状态管理库
   - 相比Redux更灵活，更少的模板代码

6. **Recoil**：
   - Facebook推出的实验性状态管理库
   - 针对React设计，支持派生状态和异步查询

7. **Zustand/Jotai**：
   - 新一代轻量级状态管理库
   - API简单，基于Hooks设计

**Redux工作原理**：

1. **核心概念**：
   - **Store**：存储应用的状态
   - **Action**：描述发生了什么的普通JS对象
   - **Reducer**：根据Action更新状态的纯函数
   - **Dispatch**：发送Action的方法
   - **Middleware**：插入到dispatch过程中的扩展点

2. **数据流向**：
   - 单向数据流：View → Action → Reducer → Store → View
   - 所有状态变更都可追踪和可预测

3. **工作流程**：
   ```jsx
   // 定义Reducer
   function counterReducer(state = { count: 0 }, action) {
     switch (action.type) {
       case 'INCREMENT':
         return { count: state.count + 1 };
       case 'DECREMENT':
         return { count: state.count - 1 };
       default:
         return state;
     }
   }
   
   // 创建Store
   const store = createStore(counterReducer);
   
   // 定义Action Creator
   function increment() {
     return { type: 'INCREMENT' };
   }
   
   // 发起Action
   store.dispatch(increment());
   
   // 订阅变化
   store.subscribe(() => console.log(store.getState()));
   ```

4. **与React集成**：
   - 通常使用react-redux库连接React组件和Redux store
   - Provider组件使store可在整个组件树中访问
   - useSelector钩子从store中选择数据
   - useDispatch钩子获取dispatch函数
   ```jsx
   // 在应用顶层提供store
   import { Provider } from 'react-redux';
   
   ReactDOM.render(
     <Provider store={store}>
       <App />
     </Provider>,
     document.getElementById('root')
   );
   
   // 在组件中使用
   import { useSelector, useDispatch } from 'react-redux';
   
   function Counter() {
     const count = useSelector((state) => state.count);
     const dispatch = useDispatch();
     
     return (
       <div>
         <p>Count: {count}</p>
         <button onClick={() => dispatch({ type: 'INCREMENT' })}>+</button>
         <button onClick={() => dispatch({ type: 'DECREMENT' })}>-</button>
       </div>
     );
   }
   ```

5. **Redux中间件**：
   - 用于处理副作用(如API调用)和异步操作
   - 常用中间件：redux-thunk、redux-saga、redux-observable
   ```jsx
   // redux-thunk示例
   function fetchUserData(userId) {
     return async function(dispatch) {
       dispatch({ type: 'FETCH_USER_REQUEST' });
       try {
         const response = await fetch(`/api/users/${userId}`);
         const data = await response.json();
         dispatch({ type: 'FETCH_USER_SUCCESS', payload: data });
       } catch (error) {
         dispatch({ type: 'FETCH_USER_FAILURE', payload: error.message });
       }
     };
   }
   
   // 使用
   dispatch(fetchUserData(123));
   ```

6. **Redux工具包(Redux Toolkit)**：
   - Redux官方推荐的工具包，简化Redux代码
   - 提供createSlice等API减少样板代码
   - 内置了常用中间件和immer以支持不可变更新写法
   ```jsx
   import { createSlice, configureStore } from '@reduxjs/toolkit';
   
   const counterSlice = createSlice({
     name: 'counter',
     initialState: { value: 0 },
     reducers: {
       increment: state => {
         state.value += 1; // 看似直接修改状态，实际上是immer处理
       },
       decrement: state => {
         state.value -= 1;
       }
     }
   });
   
   export const { increment, decrement } = counterSlice.actions;
   
   const store = configureStore({
     reducer: counterSlice.reducer
   });
   ```

**考察重点**：
- 不同状态管理方案的适用场景
- Redux的核心概念和工作流程
- 如何在React应用中正确使用Redux
- 对单向数据流的理解
- 处理异步操作的方式

### 3. React的生命周期与Hooks有什么对应关系？

**答案**：
React的类组件生命周期方法和函数组件Hooks之间有一定的对应关系，但实现机制不同。

**类组件生命周期**：

1. **挂载阶段**：
   - constructor()：初始化state和绑定方法
   - static getDerivedStateFromProps()：根据props设置state
   - render()：渲染组件
   - componentDidMount()：组件挂载后执行，常用于网络请求和DOM操作

2. **更新阶段**：
   - static getDerivedStateFromProps()
   - shouldComponentUpdate()：性能优化，决定是否重新渲染
   - render()
   - getSnapshotBeforeUpdate()：DOM更新前获取信息
   - componentDidUpdate()：组件更新后执行

3. **卸载阶段**：
   - componentWillUnmount()：组件卸载前执行，清理工作

4. **错误处理**：
   - static getDerivedStateFromError()
   - componentDidCatch()：捕获子组件错误

**函数组件Hooks与生命周期的对应关系**：

1. **useState**：
   - 对应类组件的constructor和this.setState
   - 管理组件内部状态
   ```jsx
   // 类组件
   constructor(props) {
     super(props);
     this.state = { count: 0 };
   }
   
   // 函数组件
   const [count, setCount] = useState(0);
   ```

2. **useEffect**：
   - 组合了componentDidMount、componentDidUpdate和componentWillUnmount的功能
   - 可以通过依赖数组控制执行时机
   ```jsx
   // 模拟componentDidMount
   useEffect(() => {
     // 副作用代码
     return () => {
       // 清理函数，类似componentWillUnmount
     };
   }, []); // 空依赖数组，只在挂载和卸载时执行
   
   // 模拟componentDidUpdate
   useEffect(() => {
     // 副作用代码
   }, [prop1, prop2]); // 只在依赖变化时执行
   
   // 模拟componentDidMount和componentDidUpdate
   useEffect(() => {
     // 每次渲染后执行
   }); // 没有依赖数组
   ```

3. **useLayoutEffect**：
   - 与useEffect类似，但同步执行
   - 更接近componentDidMount和componentDidUpdate的执行时机
   - 用于需要在浏览器绘制前执行的DOM测量和更新

4. **useRef**：
   - 保存在多次渲染间持久存在的可变引用
   - 常用于保存DOM元素引用，类似类组件中的createRef()
   ```jsx
   // 类组件
   constructor(props) {
     super(props);
     this.inputRef = React.createRef();
   }
   
   // 函数组件
   const inputRef = useRef(null);
   ```

5. **useMemo和useCallback**：
   - 用于性能优化，类似shouldComponentUpdate的功能
   - 避免不必要的计算和渲染
   ```jsx
   // 类似于在shouldComponentUpdate中手动比较
   const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);
   const memoizedCallback = useCallback(() => doSomething(a, b), [a, b]);
   ```

6. **useReducer**：
   - 管理复杂状态逻辑，类似Redux中的reducer
   - 适合状态逻辑复杂或下一个状态依赖当前状态的情况
   ```jsx
   const [state, dispatch] = useReducer(reducer, initialState);
   ```

7. **useContext**：
   - 订阅React context，替代Consumer组件
   - 简化跨组件共享状态
   ```jsx
   const value = useContext(MyContext);
   ```

8. **自定义Hook**：
   - 没有直接对应的类组件概念
   - 提取和复用组件逻辑，而不复用UI
   ```jsx
   function useWindowSize() {
     const [size, setSize] = useState({ width: 0, height: 0 });
     
     useEffect(() => {
       const handleResize = () => {
         setSize({ width: window.innerWidth, height: window.innerHeight });
       };
       
       window.addEventListener('resize', handleResize);
       handleResize(); // 初始化大小
       
       return () => window.removeEventListener('resize', handleResize);
     }, []);
     
     return size;
   }
   ```

**生命周期方法到Hooks的映射表**：

| 类组件生命周期方法 | Hooks对应方案 |
|------------------|-------------|
| constructor | useState, useReducer |
| getDerivedStateFromProps | useState + useEffect |
| shouldComponentUpdate | React.memo, useMemo |
| render | 函数组件本身 |
| componentDidMount | useEffect(() => {}, []) |
| componentDidUpdate | useEffect(() => {}, [dependencies]) |
| componentWillUnmount | useEffect(() => { return () => {} }, []) |
| getSnapshotBeforeUpdate | 无直接对应，可用useRef+useLayoutEffect模拟 |
| componentDidCatch | 无直接对应，需使用错误边界组件 |

**考察重点**：
- Hooks的基本用法和原理
- 生命周期方法如何用Hooks实现
- Hooks的依赖数组机制
- 自定义Hook的创建和使用
- 类组件迁移到函数组件+Hooks的思路

### 4. React性能优化的方法有哪些？

**答案**：
React应用性能优化涉及多个方面，从组件渲染控制到构建优化都有相应的技术。

**1. 避免不必要的渲染**：

- **React.memo**：缓存函数组件，只有当props变化时才重新渲染
  ```jsx
  const MemoizedComponent = React.memo(function MyComponent(props) {
    // 只有当props变化时才会重新渲染
    return <div>{props.name}</div>;
  });
  ```

- **PureComponent**：类组件的浅比较优化
  ```jsx
  class OptimizedComponent extends React.PureComponent {
    render() {
      return <div>{this.props.name}</div>;
    }
  }
  ```

- **shouldComponentUpdate**：手动控制组件更新
  ```jsx
  shouldComponentUpdate(nextProps, nextState) {
    // 自定义比较逻辑，返回true才会重新渲染
    return nextProps.id !== this.props.id;
  }
  ```

**2. 优化Hooks使用**：

- **useMemo**：缓存计算结果
  ```jsx
  const memoizedValue = useMemo(() => {
    return computeExpensiveValue(a, b);
  }, [a, b]); // 只有a或b变化时才重新计算
  ```

- **useCallback**：缓存函数引用
  ```jsx
  const memoizedCallback = useCallback(() => {
    doSomething(a, b);
  }, [a, b]); // 只有a或b变化时才创建新函数
  ```

- **合理的依赖数组**：
  ```jsx
  // 不好的例子：空依赖但使用了外部变量
  useEffect(() => {
    console.log(count); // 使用了count但没有声明依赖
  }, []); // 可能导致闭包陷阱
  
  // 好的例子：正确声明依赖
  useEffect(() => {
    console.log(count);
  }, [count]); // 当count变化时执行
  ```

**3. 列表优化**：

- **使用key属性**：帮助React识别哪些元素变化
  ```jsx
  {items.map(item => (
    <ListItem key={item.id} data={item} /> // 使用唯一ID作为key
  ))}
  ```

- **虚拟列表**：只渲染可视区域内的元素
  ```jsx
  import { FixedSizeList } from 'react-window';
  
  function List({ items }) {
    const Row = ({ index, style }) => (
      <div style={style}>Item {items[index]}</div>
    );
    
    return (
      <FixedSizeList
        height={500}
        width={300}
        itemCount={items.length}
        itemSize={50}
      >
        {Row}
      </FixedSizeList>
    );
  }
  ```

**4. 延迟加载**：

- **代码分割**：使用React.lazy和Suspense延迟加载组件
  ```jsx
  const LazyComponent = React.lazy(() => import('./LazyComponent'));
  
  function App() {
    return (
      <Suspense fallback={<div>Loading...</div>}>
        <LazyComponent />
      </Suspense>
    );
  }
  ```

- **路由级代码分割**：
  ```jsx
  import { BrowserRouter, Routes, Route } from 'react-router-dom';
  import React, { Suspense } from 'react';
  
  const Home = React.lazy(() => import('./routes/Home'));
  const About = React.lazy(() => import('./routes/About'));
  
  function App() {
    return (
      <BrowserRouter>
        <Suspense fallback={<div>Loading...</div>}>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
          </Routes>
        </Suspense>
      </BrowserRouter>
    );
  }
  ```

**5. 状态管理优化**：

- **局部状态**：将全局状态拆分为局部状态
  ```jsx
  // 不好的例子：所有状态都放在Redux中
  const { user, posts, comments, settings } = useSelector(state => state);
  
  // 好的例子：只取需要的状态
  const user = useSelector(state => state.user);
  ```

- **选择器记忆化**：使用createSelector(Reselect)缓存计算
  ```jsx
  import { createSelector } from 'reselect';
  
  const selectUsers = state => state.users;
  const selectActiveUserId = state => state.activeUserId;
  
  const selectActiveUser = createSelector(
    [selectUsers, selectActiveUserId],
    (users, activeUserId) => users.find(user => user.id === activeUserId)
  );
  ```

**6. 渲染优化**：

- **避免内联对象**：
  ```jsx
  // 不好的例子：每次渲染都创建新对象
  return <Component style={{ margin: 0 }} />;
  
  // 好的例子：使用常量或useMemo
  const style = { margin: 0 };
  return <Component style={style} />;
  ```

- **使用Fragment减少DOM节点**：
  ```jsx
  // 不好的例子：额外的div
  return (
    <div>
      <Child1 />
      <Child2 />
    </div>
  );
  
  // 好的例子：使用Fragment
  return (
    <>
      <Child1 />
      <Child2 />
    </>
  );
  ```

**7. 工具和分析**：

- **使用React DevTools分析组件渲染**
- **使用Lighthouse和Performance面板分析性能**
- **使用why-did-you-render库检测不必要的渲染**
  ```jsx
  // 安装并配置
  import React from 'react';
  import whyDidYouRender from '@welldone-software/why-did-you-render';
  whyDidYouRender(React);
  
  // 标记需要监控的组件
  function MyComponent() {
    // ...
  }
  MyComponent.whyDidYouRender = true;
  ```

**8. 构建优化**：

- **Tree Shaking**：去除未使用的代码
- **生产环境构建**：确保使用生产模式构建
- **分析构建包体积**：使用webpack-bundle-analyzer分析和优化

**9. 服务器端渲染(SSR)和静态生成(SSG)**：
- 使用Next.js等框架提升首屏加载性能
- 预渲染静态页面减少TTFB(Time To First Byte)

**10. 使用Web Workers**：
- 将耗时计算移至Web Worker
```jsx
// 创建Worker
const worker = new Worker('./worker.js');

// 组件中使用
function HeavyComponent() {
  const [result, setResult] = useState(null);
  
  useEffect(() => {
    worker.postMessage({ data: complexData });
    worker.onmessage = (e) => {
      setResult(e.data);
    };
  }, []);
  
  return <div>{result}</div>;
}
```

**考察重点**：
- 理解React渲染机制
- 组件级性能优化技术
- Hook使用的最佳实践
- 大型应用性能优化思路
- 使用合适的工具分析性能问题

### 5. 详细解释React中的Context API和它与Redux的区别

**答案**：
React的Context API是一种组件间共享数据的方式，无需通过props显式传递。它与Redux都用于状态管理，但有不同的设计思路和适用场景。

**React Context API基础**：

1. **基本概念**：
   - Context提供了一种跨组件层级传递数据的方法
   - 包含Provider(提供数据)和Consumer(消费数据)两部分
   - 避免了"prop drilling"(通过多层组件传递props)

2. **创建和使用Context**：
   ```jsx
   // 创建Context
   const ThemeContext = React.createContext('light');
   
   // Provider提供值
   function App() {
     return (
       <ThemeContext.Provider value="dark">
         <Toolbar />
       </ThemeContext.Provider>
     );
   }
   
   // 类组件消费Context
   class ThemedButton extends React.Component {
     static contextType = ThemeContext;
     render() {
       return <Button theme={this.context} />;
     }
   }
   
   // 函数组件使用useContext钩子
   function ThemedButton() {
     const theme = useContext(ThemeContext);
     return <Button theme={theme} />;
   }
   
   // 使用Consumer组件
   function ThemedButton() {
     return (
       <ThemeContext.Consumer>
         {theme => <Button theme={theme} />}
       </ThemeContext.Consumer>
     );
   }
   ```

3. **Context的特性**：
   - 嵌套Context：可以有多个Provider嵌套
   - 默认值：创建Context时可以指定默认值
   - 动态更新：Provider的value改变会导致消费该Context的组件重新渲染

4. **使用场景**：
   - 主题切换
   - 用户认证状态
   - 语言偏好
   - 路由信息
   - 全局UI状态(如模态框显示状态)

**Context与Redux的区别**：

1. **设计理念**：
   - Context：React内置特性，提供数据共享机制
   - Redux：完整的状态管理库，实现可预测的状态容器

2. **状态更新**：
   - Context：没有内置的状态更新模式，通常与useState或useReducer结合
   - Redux：通过dispatch action和reducer实现单向数据流更新

3. **功能完备性**：
   - Context：仅提供数据传递，不包含状态管理逻辑
   - Redux：提供完整的状态管理方案，包括中间件、开发工具等

4. **性能考虑**：
   - Context：Provider值变化时，所有消费该Context的组件都会重新渲染
   - Redux：通过connect或useSelector可以细粒度控制组件渲染

5. **调试能力**：
   - Context：没有专门的调试工具
   - Redux：Redux DevTools提供状态变化历史、时间旅行调试等

6. **中间件支持**：
   - Context：不支持中间件
   - Redux：支持中间件处理副作用、异步操作等

**使用Context实现简易状态管理**：
```jsx
// 创建Context和Reducer
const initialState = { count: 0 };

function reducer(state, action) {
  switch (action.type) {
    case 'increment':
      return { count: state.count + 1 };
    case 'decrement':
      return { count: state.count - 1 };
    default:
      return state;
  }
}

const StateContext = React.createContext();
const DispatchContext = React.createContext();

// 创建Provider组件
function StateProvider({ children }) {
  const [state, dispatch] = useReducer(reducer, initialState);
  
  return (
    <StateContext.Provider value={state}>
      <DispatchContext.Provider value={dispatch}>
        {children}
      </DispatchContext.Provider>
    </StateContext.Provider>
  );
}

// 自定义Hooks简化使用
function useState() {
  return useContext(StateContext);
}

function useDispatch() {
  return useContext(DispatchContext);
}

// 使用
function App() {
  return (
    <StateProvider>
      <Counter />
    </StateProvider>
  );
}

function Counter() {
  const { count } = useState();
  const dispatch = useDispatch();
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => dispatch({ type: 'increment' })}>+</button>
      <button onClick={() => dispatch({ type: 'decrement' })}>-</button>
    </div>
  );
}
```

**何时使用Context vs Redux**：

1. **使用Context的情况**：
   - 应用规模小到中等
   - 状态逻辑简单
   - 状态更新频率较低
   - 只需要共享状态，不需要复杂的状态管理功能
   - 主要是组件间数据传递问题

2. **使用Redux的情况**：
   - 大型应用
   - 复杂的状态逻辑
   - 频繁的状态更新
   - 需要缓存、预加载等高级特性
   - 需要良好的开发者工具和调试体验
   - 有大量的异步操作和副作用

3. **两者结合使用**：
   - Redux管理全局状态和业务逻辑
   - Context管理UI状态和主题等配置

**考察重点**：
- Context API的基本用法
- 使用Context实现状态管理的方法
- Context和Redux的适用场景
- 状态管理的架构设计思路
- 性能考虑因素

## Uniapp和跨端开发

### 1. Uniapp的基本原理和优势是什么？

**答案**：
Uniapp是一个基于Vue.js开发的跨平台框架，允许开发者使用一套代码构建多个平台的应用。

**基本原理**：

1. **编译器转换**：
   - Uniapp使用特有的编译器将Vue单文件组件转换为各平台的原生代码
   - 不同平台的条件编译通过特殊注释实现
   - 编译时会将框架层的API转换为对应平台的API调用

2. **运行时适配**：
   - 在小程序平台实现了一套模拟DOM和BOM的运行环境
   - 适配各平台差异，统一API调用方式
   - 提供了统一的生命周期管理机制

3. **组件和API抽象**：
   - 封装了基础组件，映射到各平台的原生组件
   - 提供统一的API接口，底层根据平台调用不同实现

**主要优势**：

1. **跨平台能力**：
   - 一套代码，同时生成iOS、Android、H5、以及各家小程序(微信、支付宝、百度、抖音、QQ等)
   - 支持App端使用原生渲染(nvue)，解决性能问题

2. **开发效率**：
   - 基于熟悉的Vue语法，学习成本低
   - 内置常用组件和API，避免重复造轮子
   - HBuilderX提供了良好的开发体验，包括智能提示、真机预览等

3. **生态丰富**：
   - 可使用npm安装第三方依赖
   - 兼容微信小程序自定义组件
   - 可共享H5端的各种组件和库
   - 内置uView等UI框架支持

4. **性能优化**：
   - App端支持原生渲染(nvue)，性能接近原生应用
   - 小程序平台使用分包加载，提高启动速度
   - 支持运行时性能优化，如按需注入

5. **统一的开发体验**：
   - 一致的开发规范和工具链
   - 统一的组件和API命名
   - 降低多端维护成本

**代码示例**：
```vue
<template>
  <view class="container">
    <text>{{ message }}</text>
    <button @click="handleClick">点击</button>
    
    <!-- 条件编译，仅在微信小程序显示 -->
    <!-- #ifdef MP-WEIXIN -->
    <button open-type="share">微信分享</button>
    <!-- #endif -->
    
    <!-- 条件编译，仅在App显示 -->
    <!-- #ifdef APP-PLUS -->
    <button @click="scanCode">扫码</button>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      message: 'Hello Uniapp'
    }
  },
  methods: {
    handleClick() {
      // 统一的API调用方式
      uni.showToast({
        title: '点击成功',
        icon: 'success'
      });
    },
    scanCode() {
      // 调用App特有功能
      uni.scanCode({
        success(res) {
          console.log(res.result);
        }
      });
    }
  }
}
</script>

<style>
.container {
  padding: 20px;
}
</style>
```

**考察重点**：
- Uniapp的工作原理
- 与其他跨平台方案的区别
- 条件编译的使用
- 各平台间差异处理方法
- 适合使用Uniapp的业务场景

### 2. Uniapp的生命周期与Vue生命周期有什么区别？

**答案**：
Uniapp结合了Vue的生命周期和小程序的生命周期，形成了一套更完整的生命周期体系。

**Vue组件生命周期（页面和组件共有）**：

1. **beforeCreate**：实例初始化前，数据观测和事件配置前调用
2. **created**：实例创建后调用，此时可访问data和methods
3. **beforeMount**：挂载开始前调用
4. **mounted**：挂载完成后调用
5. **beforeUpdate**：数据更新时，虚拟DOM重新渲染前调用
6. **updated**：数据更新后，虚拟DOM重新渲染完成后调用
7. **beforeDestroy**：实例销毁前调用
8. **destroyed**：实例销毁后调用

**Uniapp页面生命周期（仅页面组件独有）**：

1. **onLoad(options)**：页面加载时触发，参数为上个页面传递的数据
2. **onShow**：页面显示或从后台切到前台时触发
3. **onReady**：页面初次渲染完成时触发
4. **onHide**：页面隐藏或切到后台时触发
5. **onUnload**：页面卸载时触发
6. **onPullDownRefresh**：用户下拉刷新时触发
7. **onReachBottom**：页面滚动到底部时触发
8. **onTabItemTap**：点击tab时触发
9. **onShareAppMessage**：用户点击分享时触发
10. **onPageScroll**：页面滚动时触发
11. **onResize**：窗口尺寸变化时触发
12. **onNavigationBarButtonTap**：原生标题栏按钮点击时触发
13. **onBackPress**：返回按钮被点击时触发

**Uniapp应用生命周期（App.vue独有）**：

1. **onLaunch**：应用初始化完成时触发
2. **onShow**：应用启动或从后台进入前台时触发
3. **onHide**：应用从前台进入后台时触发
4. **onError**：应用报错时触发
5. **onUniNViewMessage**：接收nvue页面发送的数据
6. **onUnhandledRejection**：未处理的Promise拒绝事件
7. **onThemeChange**：系统主题变化时触发

**生命周期执行顺序**：

在页面中,生命周期执行顺序为：
`beforeCreate → created → onLoad → onShow → beforeMount → onReady → mounted`

**主要区别**：

1. **页面级生命周期**：
   - Vue只有组件生命周期
   - Uniapp额外增加了小程序风格的页面生命周期(onLoad, onShow等)
   - 页面组件同时拥有Vue组件生命周期和页面生命周期

2. **使用方式不同**：
   - Vue生命周期在选项中直接定义
   - Uniapp页面生命周期既可以在选项中定义，也可以使用Composition API形式

3. **应用级生命周期**：
   - Uniapp增加了应用级生命周期(onLaunch, onShow等)
   - 这些生命周期只能在App.vue中定义

4. **特定平台生命周期**：
   - Uniapp提供了针对特定平台的生命周期，如原生APP特有的onNavigationBarButtonTap

**代码示例**：

```vue
<script>
export default {
  // Vue组件生命周期
  beforeCreate() {
    console.log('beforeCreate');
  },
  created() {
    console.log('created');
  },
  mounted() {
    console.log('mounted');
  },
  
  // Uniapp页面生命周期
  onLoad(options) {
    console.log('页面加载', options);
  },
  onShow() {
    console.log('页面显示');
  },
  onReady() {
    console.log('页面初次渲染完成');
  },
  onHide() {
    console.log('页面隐藏');
  },
  onUnload() {
    console.log('页面卸载');
  },
  
  // 事件型生命周期
  onPullDownRefresh() {
    console.log('下拉刷新');
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
  },
  onReachBottom() {
    console.log('触底加载更多');
  }
}
</script>
```

**Composition API形式**：

```vue
<script setup>
import { onMounted, onBeforeMount } from 'vue';
import { onLoad, onShow, onReady, onHide, onUnload } from '@dcloudio/uni-app';

// Vue生命周期
onBeforeMount(() => {
  console.log('beforeMount');
});
onMounted(() => {
  console.log('mounted');
});

// Uniapp页面生命周期
onLoad((options) => {
  console.log('页面加载', options);
});
onShow(() => {
  console.log('页面显示');
});
onReady(() => {
  console.log('页面初次渲染完成');
});
</script>
```

**考察重点**：
- 了解Uniapp合并的生命周期体系
- 各类生命周期的使用场景
- 生命周期执行顺序
- Composition API形式的生命周期使用
- 不同平台下生命周期的差异

### 3. Uniapp的条件编译是什么？如何处理多平台差异？

**答案**：
条件编译是Uniapp处理多平台差异的核心机制，允许根据不同平台编译不同的代码，实现跨平台兼容。

**条件编译基础**：

1. **基本语法**：
   - 使用特殊注释包裹平台特定代码
   - 形式为：`<!-- #ifdef 平台 -->` 和 `<!-- #endif -->`
   - 或 `<!-- #ifndef 平台 -->` 和 `<!-- #endif -->`

2. **平台取值**：
   - APP-PLUS：App平台
   - H5：H5平台
   - MP-WEIXIN：微信小程序
   - MP-ALIPAY：支付宝小程序
   - MP-BAIDU：百度小程序
   - MP-TOUTIAO：抖音小程序
   - MP-QQ：QQ小程序
   - MP：所有小程序平台
   - APP-NVUE：App nvue页面

3. **应用场景**：
   - 处理平台特有功能和组件
   - 解决不同平台的样式差异
   - 针对不同平台实现不同的业务逻辑

**条件编译用法**：

1. **模板中使用**：
```html
<!-- #ifdef MP-WEIXIN -->
<view>仅在微信小程序显示</view>
<!-- #endif -->

<!-- #ifdef H5 -->
<view>仅在H5平台显示</view>
<!-- #endif -->

<!-- #ifndef MP-ALIPAY -->
<view>在除了支付宝小程序的其他平台显示</view>
<!-- #endif -->
```

2. **JS中使用**：
```javascript
// #ifdef APP-PLUS
console.log('仅在App平台输出');
// #endif

// #ifdef MP-WEIXIN || MP-QQ
function wxOrQQFunction() {
  // 微信或QQ小程序专有代码
}
// #endif

// #ifndef H5
uni.scanCode({ // 非H5平台的代码
  success(res) {
    console.log(res.result);
  }
});
// #endif
```

3. **样式中使用**：
```css
/* #ifdef APP-PLUS */
.app-box {
  padding-top: var(--status-bar-height);
}
/* #endif */

/* #ifdef MP */
.mp-box {
  border-radius: 20rpx;
}
/* #endif */
```

4. **整个文件的条件编译**：
   - 文件名命名格式：文件名.平台.后缀
   - 例如：utils.js（所有平台）、utils.app.js（仅App平台）、utils.mp.js（仅小程序平台）

5. **pages.json中使用**：
```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "app-plus": {
          "titleNView": {
            "buttons": [{
              "text": "分享",
              "fontSize": "16px"
            }]
          }
        },
        "mp-weixin": {
          "enablePullDownRefresh": true
        }
      }
    }
  ]
}
```

**处理多平台差异的其他方法**：

1. **运行环境判断**：
```javascript
// 判断平台
if (uni.getSystemInfoSync().platform === 'android') {
  // 安卓平台专有代码
}

// 获取当前平台类型
const platform = uni.getSystemInfoSync().uniPlatform;
console.log(platform); // 如：'app'、'mp-weixin'等
```

2. **API差异处理**：
```javascript
// 使用uni API统一处理
uni.showToast({
  title: '成功',
  icon: 'success'
});

// 平台特有功能的兼容处理
function share() {
  // #ifdef MP-WEIXIN
  return {
    title: '分享标题',
    path: '/pages/index/index'
  }
  // #endif
  
  // #ifdef H5
  // H5使用Web Share API或自定义分享UI
  if (navigator.share) {
    navigator.share({
      title: '分享标题',
      url: window.location.href
    });
  } else {
    showCustomShareUI();
  }
  // #endif
}
```

3. **组件差异处理**：
```vue
<template>
  <view class="container">
    <!-- 通用组件 -->
    <uni-section title="跨平台通用组件"></uni-section>
    
    <!-- 平台特有能力适配 -->
    <template v-if="isWeixin">
      <button open-type="contact">客服</button>
    </template>
    <template v-else>
      <button @click="contactUs">联系我们</button>
    </template>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isWeixin: false
    }
  },
  created() {
    // 运行时判断平台
    const info = uni.getSystemInfoSync();
    this.isWeixin = info.uniPlatform === 'mp-weixin';
  },
  methods: {
    contactUs() {
      // 非微信平台的联系方式
      uni.makePhoneCall({
        phoneNumber: '10086'
      });
    }
  }
}
</script>
```

**最佳实践**：

1. **抽象共同逻辑**：
   - 将跨平台通用逻辑抽取为公共方法
   - 只对平台差异部分使用条件编译

2. **分离平台特定代码**：
   - 创建平台特定的服务层
   - 使用工厂模式或策略模式处理平台差异

3. **减少判断逻辑**：
   - 尽量使用uni统一API
   - 对于平台特有功能，先检查功能是否可用再调用

4. **使用easycom组件规范**：
   - 组件按需引入，不需要import和注册

5. **合理使用原生渲染**：
   - 性能关键页面使用nvue开发

**考察重点**：
- 条件编译的语法和适用范围
- 处理多平台差异的策略
- 跨平台开发的最佳实践
- 平台特有能力的兼容方案
- 如何提高代码复用率

### 4. Uniapp中如何实现页面通信和状态管理？

**答案**：
Uniapp提供了多种页面通信和状态管理方案，可以根据不同场景选择合适的方式。

**页面通信方式**：

1. **页面间传参**：
   - 通过navigateTo等路由方法传递参数
   - 在目标页面的onLoad生命周期中接收参数
```javascript
// 发送页面
uni.navigateTo({
  url: '/pages/detail/detail?id=123&type=product'
});

// 接收页面
export default {
  onLoad(options) {
    console.log(options.id); // 123
    console.log(options.type); // product
  }
}
```

2. **uni.$emit / uni.$on 事件通信**：
   - 通过全局事件总线发送和接收事件
   - 适合非父子组件间通信
```javascript
// 发送事件
uni.$emit('update', { name: 'uniapp', version: '3.0' });

// 接收事件
uni.$on('update', function(data) {
  console.log('收到数据', data);
});

// 移除监听(建议在页面onUnload时移除)
uni.$off('update');
```

3. **uni.setStorage / uni.getStorage 本地数据存储**：
   - 利用本地存储在页面间共享数据
   - 适用于简单数据的持久化存储
```javascript
// 存储数据
uni.setStorage({
  key: 'userInfo',
  data: {
    name: '张三',
    id: 100
  }
});

// 或同步方式
uni.setStorageSync('token', 'abc123');

// 获取数据
uni.getStorage({
  key: 'userInfo',
  success: function (res) {
    console.log(res.data);
  }
});

// 或同步方式
const token = uni.getStorageSync('token');
```

4. **getCurrentPages() 获取页面栈**：
   - 可以获取页面栈数组，访问其他页面实例
   - 主要用于相邻页面间通信
```javascript
// 获取上一个页面实例
const pages = getCurrentPages();
const prevPage = pages[pages.length - 2]; // 上一个页面

// 调用上一个页面的方法
prevPage.$vm.refreshData();

// 修改上一个页面的数据
prevPage.$vm.dataList = [1, 2, 3];
```

**状态管理方案**：

1. **Vuex状态管理**：
   - Uniapp完全支持Vuex
   - 适合大型应用的全局状态管理

```javascript
// store.js
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    hasLogin: false,
    userInfo: {}
  },
  mutations: {
    login(state, userInfo) {
      state.hasLogin = true;
      state.userInfo = userInfo;
    },
    logout(state) {
      state.hasLogin = false;
      state.userInfo = {};
    }
  },
  actions: {
    async loginAction({ commit }, userInfo) {
      try {
        // 模拟登录请求
        const result = await new Promise(resolve => setTimeout(() => {
          resolve({ ...userInfo, id: 100 });
        }, 1000));
        
        commit('login', result);
        uni.setStorageSync('token', 'example_token');
        return result;
      } catch (e) {
        console.error(e);
      }
    }
  },
  getters: {
    isLoggedIn: state => state.hasLogin,
    username: state => state.userInfo.name || '未登录'
  }
})

// 在main.js中引入
import store from './store'
Vue.prototype.$store = store

// 在组件中使用
export default {
  computed: {
    username() {
      return this.$store.getters.username;
    }
  },
  methods: {
    async handleLogin() {
      await this.$store.dispatch('loginAction', { name: '张三' });
      uni.showToast({ title: '登录成功' });
    }
  }
}
```

2. **Pinia状态管理(Vue3)**：
   - 在Uniapp Vue3项目中使用Pinia
   - 相比Vuex更轻量且TypeScript支持更好

```javascript
// stores/counter.js
import { defineStore } from 'pinia'

export const useCounterStore = defineStore('counter', {
  state: () => ({ count: 0 }),
  actions: {
    increment() {
      this.count++;
    }
  }
})

// 在main.js中引入
import { createPinia } from 'pinia'
const pinia = createPinia()
export function createApp() {
  const app = createSSRApp(App)
  app.use(pinia)
  return { app }
}

// 在组件中使用
<script setup>
import { useCounterStore } from '@/stores/counter'

const counter = useCounterStore()
</script>

<template>
  <view>
    <text>{{ counter.count }}</text>
    <button @click="counter.increment()">+1</button>
  </view>
</template>
```

3. **uni-mini-router**：
   - 基于uni.navigateTo等API封装的路由插件
   - 提供路由拦截器和路由守卫功能

```javascript
import Router from 'uni-mini-router'
import { createPinia } from 'pinia'

const router = new Router({
  routes: [...] // 路由配置
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = uni.getStorageSync('token')
  if (to.meta.requiresAuth && !token) {
    next('/pages/login/login')
  } else {
    next()
  }
})

export function createApp() {
  const app = createSSRApp(App)
  app.use(createPinia())
  app.use(router)
  return { app }
}
```

4. **自定义状态管理(轻量级)**：
   - 对于简单应用，可以使用Vue.observable创建响应式状态
   - Vue3中可使用reactive和ref

```javascript
// Vue2版本
// store.js
import Vue from 'vue'

export const store = Vue.observable({
  userInfo: uni.getStorageSync('userInfo') || {},
  cartItems: []
})

export const mutations = {
  setUserInfo(userInfo) {
    store.userInfo = userInfo
    uni.setStorageSync('userInfo', userInfo)
  },
  addToCart(item) {
    store.cartItems.push(item)
  }
}

// 组件中使用
import { store, mutations } from '@/store.js'

export default {
  computed: {
    username() {
      return store.userInfo.name || '游客'
    }
  },
  methods: {
    login() {
      mutations.setUserInfo({ name: '张三', id: 100 })
    }
  }
}

// Vue3版本
// store.js
import { reactive, ref } from 'vue'

export const userInfo = reactive(uni.getStorageSync('userInfo') || {})
export const cartItems = reactive([])
export const counter = ref(0)

export function setUserInfo(info) {
  Object.assign(userInfo, info)
  uni.setStorageSync('userInfo', userInfo)
}

export function addToCart(item) {
  cartItems.push(item)
}

// 组件中使用
<script setup>
import { userInfo, setUserInfo, counter } from '@/store.js'

function login() {
  setUserInfo({ name: '张三', id: 100 })
}
</script>

<template>
  <view>{{ userInfo.name || '游客' }}</view>
  <button @click="login">登录</button>
  <view>计数: {{ counter }}</view>
</template>
```

**页面通信和状态管理的最佳实践**：

1. **选择合适的方案**：
   - 简单传参：使用路由参数或本地存储
   - 组件间通信：使用uni.$emit/$on或props/emit
   - 复杂应用：使用Vuex或Pinia全局状态管理

2. **性能考虑**：
   - 避免在Vuex中存储大量数据
   - 合理使用持久化，避免频繁读写本地存储
   - 使用getter进行数据派生和缓存

3. **跨端兼容**：
   - 使用uni API而非平台特定API
   - 考虑不同平台对存储大小的限制

4. **内存管理**：
   - 组件销毁时清理事件监听(uni.$off)
   - 避免闭包导致的内存泄漏

**考察重点**：
- 不同通信方式的适用场景
- Vuex/Pinia在Uniapp中的配置和使用
- 页面间数据传递的方法
- 组件通信的最佳实践
- 状态管理的设计原则

### 5. Uniapp项目实践中常见的性能优化策略有哪些？

**答案**：
Uniapp作为跨平台框架，在不同平台上有不同的性能挑战，以下是针对Uniapp的性能优化策略。

**一、应用启动优化**：

1. **分包加载**：
   - 将应用拆分为主包和子包，减小主包体积
   - 非首页内容放入子包，按需加载
```json
// pages.json
{
  "pages": [ /* 主包页面 */ ],
  "subPackages": [
    {
      "root": "pagesA",
      "pages": [{
        "path": "list/list",
        "style": { /* ... */ }
      }]
    },
    {
      "root": "pagesB",
      "pages": [ /* ... */ ]
    }
  ]
}
```

2. **首屏渲染优化**：
   - 减少首屏不必要的计算和请求
   - 使用骨架屏(Skeleton)提升用户体验
```vue
<template>
  <view>
    <template v-if="loading">
      <skeleton />
    </template>
    <template v-else>
      <!-- 实际内容 -->
    </template>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: true
    }
  },
  onLoad() {
    this.fetchData().then(() => {
      this.loading = false;
    });
  }
}
</script>
```

3. **预加载页面**：
   - 使用preloadPage预加载下一个可能访问的页面
```javascript
// 预加载详情页
uni.preloadPage({
  url: '/pages/detail/detail',
  success() {
    console.log('预加载成功');
  },
  fail() {
    console.log('预加载失败');
  }
});
```

**二、渲染性能优化**：

1. **使用nvue页面**：
   - 在App端性能关键页面使用nvue（weex渲染引擎）
   - 提供更高效的原生渲染，尤其适合复杂列表
```vue
<!-- 创建页面时选择.nvue后缀 -->
<!-- list.nvue -->
<template>
  <list class="list">
    <cell v-for="(item, index) in listData" :key="index">
      <view class="item">
        <text>{{item.title}}</text>
      </view>
    </cell>
  </list>
</template>
```

2. **长列表优化**：
   - 使用nvue的list组件实现高性能长列表
   - vue页面使用虚拟列表组件，如z-paging
```vue
<!-- vue页面使用虚拟列表 -->
<template>
  <z-paging ref="paging" v-model="dataList" @query="queryList">
    <view v-for="(item, index) in dataList" :key="index">
      <text>{{item.title}}</text>
    </view>
  </z-paging>
</template>

<script>
export default {
  data() {
    return {
      dataList: []
    }
  },
  methods: {
    queryList(pageNo, pageSize) {
      // 请求数据
      this.$api.getList({ pageNo, pageSize }).then(res => {
        this.$refs.paging.complete(res.data.list);
      });
    }
  }
}
</script>
```

3. **避免频繁的DOM更新**：
   - 合并多次数据更新
   - 使用nextTick处理DOM更新后的操作
```javascript
// 不好的做法
this.list.forEach(item => {
  // 每次赋值都会触发DOM更新
  this.selectedId = item.id;
});

// 好的做法
const selectedId = this.list[this.list.length - 1].id;
this.selectedId = selectedId;

// 使用nextTick
this.dataList = newData;
this.$nextTick(() => {
  // DOM更新后执行
  this.initChart();
});
```

**三、网络和数据优化**：

1. **请求合并与缓存**：
   - 合并多个请求，减少HTTP开销
   - 使用本地缓存减少请求次数
```javascript
// 带缓存的数据请求封装
export const cachedRequest = async (key, callback, expireTime = 60 * 1000) => {
  const cacheKey = `cache_${key}`;
  const cacheData = uni.getStorageSync(cacheKey);
  
  if (cacheData && cacheData.expire > Date.now()) {
    return cacheData.data;
  }
  
  const data = await callback();
  
  uni.setStorageSync(cacheKey, {
    data,
    expire: Date.now() + expireTime
  });
  
  return data;
};

// 使用
async function getData() {
  return await cachedRequest('userList', () => {
    return api.getUserList();
  }, 5 * 60 * 1000); // 5分钟过期
}
```

2. **图片优化**：
   - 使用适当的图片格式和尺寸
   - 实现图片懒加载
```vue
<template>
  <image :src="isShow ? src : ''" :lazy-load="true" />
</template>

<script>
export default {
  data() {
    return {
      src: 'https://example.com/image.jpg',
      isShow: false
    }
  },
  onPageScroll(e) {
    // 简单的懒加载判断
    if (e.scrollTop > 100 && !this.isShow) {
      this.isShow = true;
    }
  }
}
</script>
```

3. **预加载数据**：
   - 提前请求可能需要的数据
```javascript
// 在首页预加载常用数据
onShow() {
  this.preloadCommonData();
},
methods: {
  async preloadCommonData() {
    // 同时请求多个接口
    const [userInfo, config] = await Promise.all([
      api.getUserInfo(),
      api.getAppConfig()
    ]);
    
    // 缓存结果
    uni.setStorageSync('userInfo', userInfo);
    uni.setStorageSync('appConfig', config);
  }
}
```

**四、代码优化**：

1. **减少不必要的计算**：
   - 使用计算属性缓存结果
   - 避免在模板中进行复杂计算
```vue
<template>
  <!-- 不好的写法 -->
  <view>{{ list.filter(item => item.active).length }}</view>
  
  <!-- 好的写法 -->
  <view>{{ activeCount }}</view>
</template>

<script>
export default {
  data() {
    return {
      list: []
    }
  },
  computed: {
    activeCount() {
      return this.list.filter(item => item.active).length;
    }
  }
}
</script>
```

2. **合理使用生命周期**：
   - 在onLoad中进行初始化和数据请求
   - 避免在onShow中重复执行昂贵操作
```javascript
export default {
  data() {
    return {
      dataLoaded: false
    }
  },
  onLoad() {
    // 只在页面加载时执行一次
    this.initData();
  },
  onShow() {
    // 只在需要时刷新数据
    if (this.needRefresh) {
      this.refreshData();
      this.needRefresh = false;
    }
  }
}
```

3. **优化条件编译**：
   - 避免在单个文件中过多条件编译
   - 使用平台特定文件减少编译体积
```javascript
// 不好的做法：大量条件编译
// #ifdef APP-PLUS
// APP端大量代码
// #endif

// #ifdef MP
// 小程序大量代码
// #endif

// 好的做法：分文件管理
// utils.js - 通用代码
// utils.app.js - App特有代码
// utils.mp.js - 小程序特有代码
```

**五、UI和交互优化**：

1. **减少样式计算**：
   - 简化样式层级
   - 避免频繁改变影响布局的属性
```css
/* 避免过深的选择器嵌套 */
.parent .child .grandchild .element { /* 不推荐 */ }

/* 扁平化选择器 */
.element { /* 推荐 */ }
```

2. **避免大量绑定事件**：
   - 使用事件委托处理列表项点击
```vue
<template>
  <!-- 不好的写法：每个元素都绑定事件 -->
  <view>
    <view v-for="item in list" :key="item.id" @click="handleClick(item)">
      {{item.name}}
    </view>
  </view>
  
  <!-- 好的写法：事件委托 -->
  <view @click="handleParentClick">
    <view v-for="item in list" :key="item.id" :data-id="item.id">
      {{item.name}}
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    handleParentClick(e) {
      const id = e.target.dataset.id;
      if (id) {
        // 处理点击
        const item = this.list.find(item => item.id == id);
        if (item) {
          console.log('点击了:', item);
        }
      }
    }
  }
}
</script>
```

3. **减少页面切换动画时的卡顿**：
   - 在页面跳转前预先计算好数据
   - 避免在onLoad中进行大量计算
```javascript
// 准备跳转数据
const params = { id: 100, data: JSON.stringify(this.processedData) };

// 跳转前预处理
this.prepareForNavigation().then(() => {
  uni.navigateTo({
    url: `/pages/detail/detail?id=${params.id}&data=${encodeURIComponent(params.data)}`
  });
});
```

**六、平台特定优化**：

1. **小程序平台**：
   - 控制包体积，分包加载
   - 减少setData频率和数据量
   - 使用IntersectionObserver实现懒加载

2. **App端优化**：
   - 使用nvue实现原生渲染
   - 合理使用纯原生组件
   - 避免频繁操作DOM

3. **H5端优化**：
   - 合理使用Web Worker处理复杂计算
   - 利用浏览器缓存机制
   - 考虑SSR提升首屏加载速度

**七、工程化优化**：

1. **代码拆分**：
   - 使用动态导入(import())延迟加载组件
```javascript
// 动态加载组件
const DetailComponent = () => import('@/components/Detail.vue');

export default {
  components: {
    DetailComponent
  }
}
```

2. **Tree Shaking**：
   - 只引入需要的模块和方法
```javascript
// 不好的做法：引入整个库
import _ from 'lodash';

// 好的做法：只引入需要的方法
import debounce from 'lodash/debounce';
```

3. **优化构建配置**：
   - 使用生产环境构建移除调试代码
   - 配置合理的环境变量控制功能

**综合优化方案**：

1. **性能监控**：
   - 实现简单的性能检测工具
```javascript
// 简单的页面性能监控
export default {
  data() {
    return {
      pageStartTime: 0
    }
  },
  onLoad() {
    this.pageStartTime = Date.now();
  },
  onReady() {
    console.log(`页面渲染耗时: ${Date.now() - this.pageStartTime}ms`);
  }
}
```

2. **定期审计与优化**：
   - 建立性能基准
   - 使用Chrome DevTools分析Web版性能

3. **渐进式优化**：
   - 先解决影响用户体验的主要问题
   - 平衡开发成本和性能提升

**考察重点**：
- Uniapp不同平台的性能特点
- 页面渲染和数据处理的优化技巧
- 长列表的高效实现方式
- 减少网络请求的策略
- 平台特定优化方法

## 前端性能优化

### 1. 前端性能优化的关键指标有哪些？如何进行性能监测？

**答案**：
前端性能优化需要关注一系列关键指标，并使用适当的工具进行监测和分析。

**关键性能指标**：

1. **加载性能指标**：
   - **First Contentful Paint (FCP)**：首次内容绘制，标记浏览器渲染DOM中第一个内容的时间
   - **Largest Contentful Paint (LCP)**：最大内容绘制，标记最大内容元素渲染完成的时间
   - **Time to Interactive (TTI)**：可交互时间，页面完全可交互所需时间
   - **Total Blocking Time (TBT)**：总阻塞时间，FCP到TTI之间主线程被阻塞的总时间
   - **First Input Delay (FID)**：首次输入延迟，用户首次交互到浏览器响应的时间
   - **Cumulative Layout Shift (CLS)**：累积布局偏移，测量视觉稳定性

2. **运行时性能指标**：
   - **帧率(FPS)**：每秒渲染的帧数，流畅体验通常需要保持60fps
   - **CPU使用率**：JavaScript执行时CPU消耗情况
   - **内存使用**：内存占用和可能的内存泄漏
   - **长任务(Long tasks)**：执行时间超过50ms的任务
   - **事件响应延迟**：用户操作后响应时间

3. **网络性能指标**：
   - **资源加载时间**：各类资源(JS、CSS、图片等)的加载耗时
   - **请求数量**：HTTP请求的数量
   - **传输大小**：资源和数据的总下载大小
   - **缓存命中率**：资源从浏览器缓存获取的比例

**性能监测工具和方法**：

1. **浏览器开发者工具**：
   - **Chrome DevTools Performance面板**：记录和分析运行时性能
   ```javascript
   // 在代码中手动标记性能测量点
   performance.mark('myTask-start');
   // 执行任务...
   performance.mark('myTask-end');
   performance.measure('myTask', 'myTask-start', 'myTask-end');
   ```
   
   - **Network面板**：分析网络请求和资源加载
   - **Memory面板**：监控内存使用和检测泄漏
   - **Lighthouse**：对网站进行全面的性能、可访问性、SEO等审计

2. **Web Vitals API**：
   ```javascript
   // 使用web-vitals库测量核心指标
   import {getCLS, getFID, getLCP} from 'web-vitals';

   function sendToAnalytics(metric) {
     const body = JSON.stringify({
       name: metric.name,
       value: metric.value,
       id: metric.id
     });
     navigator.sendBeacon('/analytics', body);
   }

   getCLS(sendToAnalytics);
   getFID(sendToAnalytics);
   getLCP(sendToAnalytics);
   ```

3. **Performance API**：
   ```javascript
   // 获取导航和资源计时信息
   const perfData = window.performance.timing;
   const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
   console.log(`页面加载时间: ${pageLoadTime}ms`);
   
   // 获取资源加载时间
   const resources = performance.getEntriesByType('resource');
   resources.forEach(resource => {
     console.log(`${resource.name}: ${resource.duration}ms`);
   });
   ```

4. **第三方监控服务**：
   - Google Analytics
   - New Relic
   - Datadog
   - Sentry
   - 阿里云ARMS

5. **自定义性能监控**：
   ```javascript
   // 简单的性能监控SDK示例
   class PerformanceMonitor {
     constructor() {
       this.metrics = {};
       this.initObservers();
     }
     
     initObservers() {
       // LCP监测
       new PerformanceObserver((entryList) => {
         const entries = entryList.getEntries();
         const lastEntry = entries[entries.length - 1];
         this.metrics.lcp = lastEntry.startTime;
         console.log(`LCP: ${this.metrics.lcp}ms`);
       }).observe({ type: 'largest-contentful-paint', buffered: true });
       
       // CLS监测
       let clsValue = 0;
       new PerformanceObserver((entryList) => {
         for (const entry of entryList.getEntries()) {
           if (!entry.hadRecentInput) {
             clsValue += entry.value;
           }
         }
         this.metrics.cls = clsValue;
         console.log(`CLS: ${this.metrics.cls}`);
       }).observe({ type: 'layout-shift', buffered: true });
       
       // 长任务监测
       new PerformanceObserver((entryList) => {
         for (const entry of entryList.getEntries()) {
           console.log(`Long task detected: ${entry.duration}ms`);
         }
       }).observe({ type: 'longtask', buffered: true });
     }
     
     // 其他监测方法...
   }
   
   // 使用
   const monitor = new PerformanceMonitor();
   ```

**分析和解读性能数据**：

1. **核心Web指标目标值**：
   - LCP：<= 2.5秒为"良好"
   - FID：<= 100毫秒为"良好"
   - CLS：<= 0.1为"良好"

2. **性能预算**：
   - 设定关键指标的目标值
   - 例如：主包JS不超过300KB，首屏加载时间不超过2秒

3. **性能对比**：
   - 与行业基准比较
   - 与历史数据比较，识别退化
   - A/B测试不同优化方案

4. **用户体验关联**：
   - 将性能指标与业务指标(转化率、跳出率等)关联
   - 根据真实用户数据(RUM)识别优化重点

**常见性能问题的诊断方法**：

1. **加载缓慢**：
   - 使用瀑布图分析资源加载顺序和时间
   - 检查大文件和阻塞资源

2. **运行时卡顿**：
   - 使用Performance面板记录帧率下降
   - 分析JavaScript执行和渲染时间
   - 查找长任务和布局抖动

3. **内存问题**：
   - 使用Memory面板录制内存快照
   - 比较不同时间点的内存占用
   - 查找DOM节点数量异常增长

**实际应用示例**：

```javascript
// 页面加载性能测量
document.addEventListener('DOMContentLoaded', () => {
  // 计算关键时间点
  const timing = performance.timing;
  const metrics = {
    // DNS查询时间
    dns: timing.domainLookupEnd - timing.domainLookupStart,
    // TCP连接时间
    tcp: timing.connectEnd - timing.connectStart,
    // 请求响应时间
    request: timing.responseEnd - timing.requestStart,
    // DOM解析时间
    dom: timing.domComplete - timing.domLoading,
    // 页面加载总时间
    load: timing.loadEventEnd - timing.navigationStart,
    // 首次绘制时间(近似值)
    firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
  };
  
  // 收集关键资源加载时间
  const resources = performance.getEntriesByType('resource');
  const resourceTiming = resources.map(item => ({
    name: item.name.split('/').pop(),
    type: item.initiatorType,
    duration: Math.round(item.duration),
    size: item.transferSize
  }));
  
  // 发送数据到分析服务器
  navigator.sendBeacon('/performance', JSON.stringify({
    metrics,
    resources: resourceTiming,
    userAgent: navigator.userAgent,
    timestamp: Date.now()
  }));
});

// 监测运行时性能
let lastFrameTime = performance.now();
let frameCounter = 0;
let slowFrames = 0;

function checkFrameRate() {
  const now = performance.now();
  const delta = now - lastFrameTime;
  
  // 计算帧时间，超过16.7ms(60fps)视为慢帧
  if (delta > 16.7) {
    slowFrames++;
  }
  
  frameCounter++;
  lastFrameTime = now;
  
  // 每秒计算一次FPS
  if (frameCounter === 60) {
    const fps = Math.round(1000 / (delta / frameCounter));
    console.log(`当前FPS: ${fps}, 慢帧比例: ${slowFrames / frameCounter}`);
    
    // 重置计数器
    frameCounter = 0;
    slowFrames = 0;
  }
  
  requestAnimationFrame(checkFrameRate);
}

// 启动帧率检测
requestAnimationFrame(checkFrameRate);
```

**考察重点**：
- 掌握关键性能指标的含义和重要性
- 使用浏览器工具和API进行性能监测
- 分析性能数据并找出优化方向
- 设计性能监控系统
- 与业务指标结合评估性能优化效果

### 2. 详细介绍前端常用的性能优化策略和方法

**答案**：
前端性能优化是提升用户体验和业务指标的重要手段，可以从网络传输、资源加载、渲染性能、运行时效率等多个方面进行。

**一、网络优化**：

1. **减少HTTP请求**：
   - 合并文件(CSS/JavaScript)
   - 使用CSS Sprites合并图片
   - 使用字体图标或SVG图标
   - 使用DataURI嵌入小图片

2. **HTTP缓存优化**：
   ```html
   <!-- 设置强缓存和协商缓存 -->
   <meta http-equiv="Cache-Control" content="max-age=86400">
   ```
   ```javascript
   // 服务端设置缓存头
   res.setHeader('Cache-Control', 'max-age=86400');
   res.setHeader('ETag', etag);
   ```

3. **CDN加速**：
   ```html
   <script src="https://cdn.example.com/libs/vue.min.js"></script>
   ```

4. **启用Gzip/Brotli压缩**：
   ```javascript
   // Nginx配置示例
   gzip on;
   gzip_comp_level 6;
   gzip_types text/plain text/css application/javascript;
   ```

5. **HTTP/2和HTTP/3**：
   - 多路复用减少TCP连接数
   - 服务器推送关键资源
   - 头部压缩减少传输量

6. **预连接和预获取**：
   ```html
   <!-- 预连接 -->
   <link rel="preconnect" href="https://api.example.com">
   
   <!-- DNS预解析 -->
   <link rel="dns-prefetch" href="https://api.example.com">
   
   <!-- 预加载关键资源 -->
   <link rel="preload" href="critical.css" as="style">
   
   <!-- 预获取可能需要的资源 -->
   <link rel="prefetch" href="next-page.js">
   ```

**二、资源优化**：

1. **图片优化**：
   - 使用WebP、AVIF等现代图片格式
   - 响应式图片，根据设备提供合适尺寸
   ```html
   <picture>
     <source srcset="image.webp" type="image/webp">
     <source srcset="image.jpg" type="image/jpeg">
     <img src="image.jpg" alt="响应式图片">
   </picture>
   ```
   - 图片懒加载
   ```html
   <img loading="lazy" src="image.jpg" alt="懒加载图片">
   ```
   ```javascript
   // 自定义懒加载
   const observer = new IntersectionObserver((entries) => {
     entries.forEach(entry => {
       if (entry.isIntersecting) {
         const img = entry.target;
         img.src = img.dataset.src;
         observer.unobserve(img);
       }
     });
   });
   
   document.querySelectorAll('img[data-src]').forEach(img => {
     observer.observe(img);
   });
   ```

2. **JavaScript优化**：
   - 代码分割(Code Splitting)
   ```javascript
   // 使用动态import
   const AdminDashboard = () => import('./AdminDashboard.js');
   
   // Webpack配置
   module.exports = {
     optimization: {
       splitChunks: {
         chunks: 'all'
       }
     }
   };
   ```
   
   - Tree Shaking去除无用代码
   ```javascript
   // 只导入需要的组件和函数
   import { Button } from 'antd';
   import { debounce } from 'lodash-es';
   ```
   
   - 压缩和混淆(minify/uglify)
   ```javascript
   // terser配置示例
   const config = {
     compress: {
       drop_console: true,
       pure_funcs: ['console.log']
     }
   };
   ```

3. **CSS优化**：
   - 精简CSS，去除未使用的样式
   ```javascript
   // 使用PurgeCSS
   const purgecss = require('purgecss');
   const result = purgecss.purge({
     content: ['**/*.html'],
     css: ['**/*.css']
   });
   ```
   
   - 避免@import，减少阻塞
   - 使用更高效的选择器
   ```css
   /* 不推荐 */
   .box * { color: red; }
   
   /* 推荐 */
   .box > .item { color: red; }
   ```

4. **字体优化**：
   - 字体子集化，只包含使用的字符
   - 使用font-display控制字体加载行为
   ```css
   @font-face {
     font-family: 'MyFont';
     src: url('myfont.woff2') format('woff2');
     font-display: swap; /* 先使用系统字体，字体加载好后再替换 */
   }
   ```

**三、渲染优化**：

1. **关键渲染路径优化**：
   - 内联关键CSS
   ```html
   <style>
     /* 首屏关键样式 */
     .header { ... }
     .banner { ... }
   </style>
   ```
   
   - 延迟加载非关键JavaScript
   ```html
   <script defer src="non-critical.js"></script>
   ```
   
   - 避免渲染阻塞资源
   ```html
   <link rel="stylesheet" href="print.css" media="print">
   ```

2. **减少重排和重绘**：
   - 批量修改DOM
   ```javascript
   // 不好的做法
   for (let i = 0; i < 100; i++) {
     el.style.top = i + 'px';
   }
   
   // 好的做法
   el.style.cssText = 'top: 100px;';
   ```
   
   - 使用transform和opacity进行动画
   ```css
   /* 不好的做法 */
   .box {
     animation: move 1s;
   }
   @keyframes move {
     from { left: 0; top: 0; }
     to { left: 100px; top: 100px; }
   }
   
   /* 好的做法 */
   .box {
     animation: move 1s;
   }
   @keyframes move {
     from { transform: translate(0, 0); }
     to { transform: translate(100px, 100px); }
   }
   ```
   
   - 使用will-change提示浏览器
   ```css
   .box {
     will-change: transform, opacity;
   }
   ```

3. **虚拟列表**：
   ```javascript
   // React示例
   function VirtualList({ items, itemHeight, visibleItems }) {
     const [scrollTop, setScrollTop] = useState(0);
     const startIndex = Math.floor(scrollTop / itemHeight);
     const endIndex = Math.min(
       startIndex + visibleItems,
       items.length
     );
     
     const visibleData = items.slice(startIndex, endIndex);
     
     return (
       <div
         style={{ height: `${items.length * itemHeight}px`, position: 'relative' }}
         onScroll={(e) => setScrollTop(e.target.scrollTop)}
       >
         <div style={{ 
           transform: `translateY(${startIndex * itemHeight}px)`,
           position: 'absolute'
         }}>
           {visibleData.map(item => (
             <div key={item.id} style={{ height: `${itemHeight}px` }}>
               {item.content}
             </div>
           ))}
         </div>
       </div>
     );
   }
   ```

**四、代码优化**：

1. **防抖和节流**：
   ```javascript
   // 防抖
   function debounce(fn, delay) {
     let timer = null;
     return function(...args) {
       clearTimeout(timer);
       timer = setTimeout(() => {
         fn.apply(this, args);
       }, delay);
     };
   }
   
   // 节流
   function throttle(fn, interval) {
     let last = 0;
     return function(...args) {
       const now = Date.now();
       if (now - last >= interval) {
         last = now;
         fn.apply(this, args);
       }
     };
   }
   
   // 使用
   const debouncedSearch = debounce(search, 300);
   const throttledScroll = throttle(onScroll, 100);
   ```

2. **Web Worker处理密集计算**：
   ```javascript
   // main.js
   const worker = new Worker('worker.js');
   
   worker.postMessage({ data: largeArray });
   
   worker.onmessage = function(e) {
     console.log('计算结果：', e.data);
   };
   
   // worker.js
   self.onmessage = function(e) {
     const result = complexCalculation(e.data.data);
     self.postMessage(result);
   };
   ```

3. **缓存计算结果**：
   ```javascript
   // 使用memoization优化计算
   function memoize(fn) {
     const cache = new Map();
     return function(...args) {
       const key = JSON.stringify(args);
       if (cache.has(key)) {
         return cache.get(key);
       }
       const result = fn.apply(this, args);
       cache.set(key, result);
       return result;
     };
   }
   
   // 使用
   const expensiveFunction = memoize(function(n) {
     console.log('计算中...');
     return n * n;
   });
   
   console.log(expensiveFunction(5)); // 计算中... 25
   console.log(expensiveFunction(5)); // 25 (直接从缓存获取)
   ```

4. **优化循环和递归**：
   ```javascript
   // 不好的做法
   for (let i = 0; i < array.length; i++) {
     // 每次迭代都要读取array.length
   }
   
   // 好的做法
   for (let i = 0, len = array.length; i < len; i++) {
     // 只需要读取一次array.length
   }
   
   // 尾递归优化
   function factorial(n, acc = 1) {
     if (n <= 1) return acc;
     return factorial(n - 1, n * acc);
   }
   ```

**五、框架相关优化**：

1. **React优化**：
   - 使用memo、useMemo和useCallback避免不必要渲染
   ```jsx
   const MemoComponent = React.memo(function MyComponent(props) {
     // 只有当props变化时才会重新渲染
     return <div>{props.name}</div>;
   });
   
   function App() {
     const [count, setCount] = useState(0);
     
     // 缓存计算结果
     const expensiveValue = useMemo(() => {
       return computeExpensiveValue(count);
     }, [count]);
     
     // 缓存回调函数
     const handleClick = useCallback(() => {
       setCount(c => c + 1);
     }, []);
     
     return (
       <div>
         <p>{expensiveValue}</p>
         <MemoComponent name="John" onClick={handleClick} />
       </div>
     );
   }
   ```
   
   - 使用PureComponent或shouldComponentUpdate控制更新
   - 使用React.lazy和Suspense进行代码分割

2. **Vue优化**：
   - 使用v-show替代频繁切换的v-if
   - 为列表项提供唯一key
   - 使用计算属性缓存计算结果
   ```vue
   <template>
     <div>
       <p>{{ expensiveComputed }}</p>
     </div>
   </template>
   
   <script>
   export default {
     data() {
       return {
         items: []
       }
     },
     computed: {
       expensiveComputed() {
         // 结果会被缓存
         return this.items.filter(/*复杂过滤逻辑*/);
       }
     }
   }
   </script>
   ```
   
   - 使用v-once渲染静态内容
   - Vue3的静态提升(hoisting)和Fragments特性

**六、用户体验优化**：

1. **骨架屏(Skeleton Screen)**：
   ```jsx
   function ProductDetail({ id }) {
     const [loading, setLoading] = useState(true);
     const [product, setProduct] = useState(null);
     
     useEffect(() => {
       fetchProduct(id).then(data => {
         setProduct(data);
         setLoading(false);
       });
     }, [id]);
     
     return (
       <div>
         {loading ? (
           <SkeletonLoader />
         ) : (
           <ProductInfo product={product} />
         )}
       </div>
     );
   }
   ```

2. **PRPL模式**：
   - Push (推送)关键资源
   - Render (渲染)初始路由
   - Pre-cache (预缓存)剩余路由
   - Lazy-load (懒加载)其他路由和资源

3. **渐进式加载**：
   - 图片渐进式加载，先显示低质量模糊版本
   - 内容逐步浮现
   - 优先加载可视区域内容

**七、构建和部署优化**：

1. **现代构建工具**：
   - Webpack、Rollup、Vite等根据场景选择
   - 生产环境优化配置
   ```javascript
   // webpack.config.js生产环境配置示例
   module.exports = {
     mode: 'production',
     optimization: {
       minimize: true,
       splitChunks: { chunks: 'all' },
       runtimeChunk: 'single'
     }
   };
   ```

2. **现代浏览器与传统浏览器差异化打包**：
   ```html
   <!-- 为现代浏览器提供ESM版本 -->
   <script type="module" src="app.mjs"></script>
   <!-- 为旧浏览器提供降级版本 -->
   <script nomodule src="app.js"></script>
   ```

3. **前端监控**：
   - 错误监控与性能监控
   - 用户行为分析
   - 日志上报与分析

**综合示例**：构建高性能电商产品列表：

```jsx
// 优化实践综合示例
function ProductList() {
  // 使用分页和虚拟列表
  const [page, setPage] = useState(1);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  
  // 使用防抖的搜索函数
  const debouncedSearch = useCallback(
    debounce((term) => {
      searchProducts(term);
    }, 300),
    []
  );
  
  // 数据获取与缓存
  useEffect(() => {
    // 检查缓存
    const cachedData = sessionStorage.getItem(`products_page_${page}`);
    if (cachedData) {
      setProducts(JSON.parse(cachedData));
      setLoading(false);
      return;
    }
    
    // 获取数据
    fetchProducts(page)
      .then(data => {
        setProducts(data);
        // 缓存数据
        sessionStorage.setItem(`products_page_${page}`, JSON.stringify(data));
        
        // 预获取下一页
        if (page < 5) {
          prefetchNextPage(page + 1);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [page]);
  
  // 预获取函数
  const prefetchNextPage = (nextPage) => {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = `/api/products?page=${nextPage}`;
    document.head.appendChild(link);
  };
  
  // 图片懒加载组件
  const LazyImage = ({ src, alt }) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const imgRef = useRef();
    
    useEffect(() => {
      const observer = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting) {
          imgRef.current.src = src;
          observer.unobserve(imgRef.current);
        }
      });
      
      if (imgRef.current) {
        observer.observe(imgRef.current);
      }
      
      return () => {
        if (imgRef.current) {
          observer.unobserve(imgRef.current);
        }
      };
    }, [src]);
    
    return (
      <div className="image-container">
        {!isLoaded && <div className="placeholder"></div>}
        <img
          ref={imgRef}
          alt={alt}
          className={isLoaded ? 'loaded' : ''}
          onLoad={() => setIsLoaded(true)}
        />
      </div>
    );
  };
  
  return (
    <div>
      {/* 搜索框 */}
      <input
        type="text"
        onChange={(e) => debouncedSearch(e.target.value)}
        placeholder="搜索产品..."
      />
      
      {/* 产品列表 */}
      {loading ? (
        <ProductListSkeleton />
      ) : (
        <div className="product-grid">
          {products.map(product => (
            <div key={product.id} className="product-card">
              <LazyImage src={product.image} alt={product.name} />
              <h3>{product.name}</h3>
              <p>${product.price}</p>
            </div>
          ))}
        </div>
      )}
      
      {/* 分页控件 */}
      <Pagination
        current={page}
        onChange={setPage}
        // 使用节流防止频繁切换
        onPageButtonClick={throttle(handlePageClick, 300)}
      />
    </div>
  );
}
```

**考察重点**：
- 全面了解各层面的性能优化方法
- 理解优化措施的原理和适用场景
- 能够结合实际业务场景选择合适的优化策略
- 掌握常见框架的性能优化特性
- 能够实施和评估优化效果

## 构建工具和工程化

### 1. 前端工程化的意义是什么？包含哪些方面？

**答案**：
前端工程化是指将前端开发流程规范化、标准化、自动化，提高开发效率和代码质量，解决前端复杂度不断提升带来的挑战。

**前端工程化的意义**：

1. **提高开发效率**：
   - 自动化构建减少重复劳动
   - 代码复用提高开发速度
   - 标准化工作流减少沟通成本

2. **保证代码质量**：
   - 代码规范统一团队编码风格
   - 自动化测试提前发现问题
   - 持续集成/持续部署确保质量

3. **优化用户体验**：
   - 性能优化加快加载速度
   - 可访问性增强提高可用性
   - 兼容性处理支持多种环境

4. **应对复杂度挑战**：
   - 模块化管理大型代码库
   - 组件化拆分复杂界面
   - 状态管理处理复杂交互

5. **提升可维护性**：
   - 文档自动化便于交接和协作
   - 版本控制记录变更历史
   - 代码结构清晰易于理解

**前端工程化的主要方面**：

1. **模块化与组件化**：
   - **模块化**：将代码拆分为独立的功能模块
     ```javascript
     // ESM模块
     export function helper() { /* ... */ }
     import { helper } from './utils';
     
     // CommonJS模块
     module.exports = { helper: function() { /* ... */ } };
     const { helper } = require('./utils');
     ```
   
   - **组件化**：UI拆分为可复用组件
     ```jsx
     // React组件示例
     function Button({ text, onClick }) {
       return <button onClick={onClick}>{text}</button>;
     }
     
     // 使用组件
     <Button text="提交" onClick={handleSubmit} />
     ```

2. **规范化与标准化**：
   - **编码规范**：ESLint、Prettier等工具
     ```json
     // .eslintrc
     {
       "extends": ["airbnb", "prettier"],
       "rules": {
         "semi": ["error", "always"],
         "quotes": ["error", "single"]
       }
     }
     ```
   
   - **目录规范**：统一的项目结构
     ```
     src/
     ├── assets/      # 静态资源
     ├── components/  # 公共组件
     ├── pages/       # 页面组件
     ├── services/    # API服务
     ├── utils/       # 工具函数
     └── App.js       # 入口组件
     ```
   
   - **提交规范**：Commitlint、Husky等
     ```json
     // commitlint.config.js
     module.exports = {
       extends: ['@commitlint/config-conventional'],
       rules: {
         'type-enum': [2, 'always', [
           'feat', 'fix', 'docs', 'style', 'refactor', 'perf', 'test', 'chore'
         ]]
       }
     };
     ```

3. **构建与自动化**：
   - **构建工具**：Webpack、Vite、Rollup等
     ```javascript
     // webpack.config.js
     module.exports = {
       entry: './src/index.js',
       output: {
         filename: 'bundle.js',
         path: path.resolve(__dirname, 'dist'),
       },
       module: {
         rules: [
           {
             test: /\.js$/,
             use: 'babel-loader',
             exclude: /node_modules/
           }
         ]
       }
     };
     ```
   
   - **任务自动化**：npm scripts、Gulp等
     ```json
     // package.json
     {
       "scripts": {
         "dev": "vite",
         "build": "vite build",
         "lint": "eslint src",
         "test": "jest"
       }
     }
     ```
   
   - **持续集成/持续部署**：Jenkins、GitLab CI、GitHub Actions等
     ```yaml
     # .github/workflows/deploy.yml
     name: Deploy
     on:
       push:
         branches: [ main ]
     jobs:
       build-and-deploy:
         runs-on: ubuntu-latest
         steps:
           - uses: actions/checkout@v2
           - name: Install dependencies
             run: npm ci
           - name: Build
             run: npm run build
           - name: Deploy
             uses: peaceiris/actions-gh-pages@v3
             with:
               github_token: ${{ secrets.GITHUB_TOKEN }}
               publish_dir: ./dist
     ```

4. **测试与质量保障**：
   - **单元测试**：Jest、Mocha等
     ```javascript
     // sum.test.js
     import { sum } from './math';
     
     test('adds 1 + 2 to equal 3', () => {
       expect(sum(1, 2)).toBe(3);
     });
     ```
   
   - **集成测试**：Cypress、Puppeteer等
     ```javascript
     // cypress/integration/login.spec.js
     describe('Login', () => {
       it('successfully logs in', () => {
         cy.visit('/login');
         cy.get('input[name=username]').type('user');
         cy.get('input[name=password]').type('pass');
         cy.get('button[type=submit]').click();
         cy.url().should('include', '/dashboard');
       });
     });
     ```
   
   - **代码覆盖率**：Istanbul等
     ```json
     // jest.config.js
     module.exports = {
       collectCoverage: true,
       coverageThreshold: {
         global: {
           statements: 80,
           functions: 80,
           lines: 80
         }
       }
     };
     ```

5. **性能与体验优化**：
   - **代码分割**：按路由/组件拆分
     ```javascript
     // React.lazy示例
     const Dashboard = React.lazy(() => import('./Dashboard'));
     
     function App() {
       return (
         <Suspense fallback={<Spinner />}>
           <Dashboard />
         </Suspense>
       );
     }
     ```
   
   - **资源优化**：压缩、Tree Shaking、懒加载
     ```javascript
     // webpack.prod.js
     const TerserPlugin = require('terser-webpack-plugin');
     
     module.exports = {
       optimization: {
         minimize: true,
         minimizer: [new TerserPlugin()],
         usedExports: true, // Tree Shaking
         splitChunks: {
           chunks: 'all'
         }
       }
     };
     ```
   
   - **预渲染与SSR**：提高首屏加载速度
     ```javascript
     // Next.js示例
     export async function getServerSideProps() {
       const res = await fetch('https://api.example.com/data');
       const data = await res.json();
       
       return { props: { data } };
     }
     ```

6. **监控与分析**：
   - **错误监控**：Sentry等
     ```javascript
     // 初始化Sentry
     Sentry.init({
       dsn: 'https://<EMAIL>/0',
       integrations: [new BrowserTracing()],
       tracesSampleRate: 1.0,
     });
     
     // 捕获错误
     try {
       someFunction();
     } catch (error) {
       Sentry.captureException(error);
     }
     ```
   
   - **性能监控**：Lighthouse、Web Vitals等
     ```javascript
     // 监控核心Web指标
     import { getCLS, getFID, getLCP } from 'web-vitals';
     
     function sendToAnalytics({ name, delta, id }) {
       // 发送到分析服务
       console.log(`Metric: ${name} | Value: ${delta} | ID: ${id}`);
     }
     
     getCLS(sendToAnalytics);
     getFID(sendToAnalytics);
     getLCP(sendToAnalytics);
     ```
   
   - **用户行为分析**：热图、用户路径等
     ```javascript
     // 简单的用户交互记录
     document.addEventListener('click', (e) => {
       const { clientX, clientY, target } = e;
       const data = {
         x: clientX,
         y: clientY,
         element: target.tagName,
         time: new Date().toISOString()
       };
       logInteraction(data);
     });
     ```

**前端工程化的实践案例**：

1. **大型电商平台**：
   - 微前端架构拆分业务模块
   - 组件库统一UI风格
   - 自动化测试保证功能稳定
   - CI/CD加速迭代发布

2. **中后台管理系统**：
   - 权限管理方案
   - 可配置化表单和列表
   - 主题定制与换肤
   - 多环境部署流程

3. **跨平台应用**：
   - 一套代码多端运行
   - 差异化打包和条件编译
   - 自动化构建多平台产物
   - 统一的API抽象层

**工程化体系建设流程**：

1. **评估与规划**：
   - 分析团队和项目需求
   - 选择适合的技术栈和工具
   - 制定阶段性目标和计划

2. **基础设施建设**：
   - 项目脚手架
   - 构建系统
   - 开发规范

3. **落地与优化**：
   - 团队培训和推广
   - 持续收集反馈
   - 迭代优化工具和流程

4. **度量与改进**：
   - 建立度量指标
   - 收集数据分析
   - 持续改进工程体系

**考察重点**：
- 前端工程化的本质和价值
- 各方面工程化实践的具体方法
- 工程化工具的选择和使用
- 如何建立和优化工程化体系
- 工程化对前端项目的实际影响

### 2. 详细介绍Webpack的核心概念和工作原理

**答案**：
Webpack是一个现代JavaScript应用程序的静态模块打包工具，它将项目的各种资源视为模块，通过分析模块依赖关系，最终生成优化后的静态资源。

**核心概念**：

1. **入口(Entry)**：
   - 指定webpack开始构建的起点模块
   - 可以有单个或多个入口
   ```javascript
   // 单入口配置
   module.exports = {
     entry: './src/index.js'
   };
   
   // 多入口配置
   module.exports = {
     entry: {
       main: './src/main.js',
       admin: './src/admin.js'
     }
   };
   ```

2. **输出(Output)**：
   - 定义webpack如何输出打包结果
   - 包括路径、文件名等配置
   ```javascript
   const path = require('path');
   
   module.exports = {
     output: {
       path: path.resolve(__dirname, 'dist'),
       filename: '[name].[contenthash].js',
       publicPath: '/assets/'
     }
   };
   ```

3. **加载器(Loader)**：
   - 转换非JavaScript模块为可处理的形式
   - Webpack本身只理解JavaScript和JSON
   ```javascript
   module.exports = {
     module: {
       rules: [
         {
           test: /\.css$/,
           use: ['style-loader', 'css-loader']
         },
         {
           test: /\.js$/,
           exclude: /node_modules/,
           use: {
             loader: 'babel-loader',
             options: {
               presets: ['@babel/preset-env']
             }
           }
         },
         {
           test: /\.(png|svg|jpg|jpeg|gif)$/i,
           type: 'asset/resource'
         }
       ]
     }
   };
   ```

4. **插件(Plugin)**：
   - 执行更广泛的任务，如打包优化、资源管理等
   - 在webpack打包的整个生命周期都能访问
   ```javascript
   const HtmlWebpackPlugin = require('html-webpack-plugin');
   const MiniCssExtractPlugin = require('mini-css-extract-plugin');
   
   module.exports = {
     plugins: [
       new HtmlWebpackPlugin({
         template: './src/index.html',
         filename: 'index.html'
       }),
       new MiniCssExtractPlugin({
         filename: '[name].[contenthash].css'
       })
     ]
   };
   ```

5. **模式(Mode)**：
   - 启用相应环境的内置优化
   - 可选值：development, production, none
   ```javascript
   module.exports = {
     mode: 'production' // 会启用压缩、优化等生产环境特性
   };
   ```

6. **模块(Module)**：
   - webpack中的模块不仅是js文件，也包括css、图片等
   - 通过loader可以支持各种类型的模块
   ```javascript
   // JavaScript模块
   import { sum } from './math';
   
   // CSS模块
   import './style.css';
   
   // 图片模块
   import logo from './logo.png';
   ```

7. **依赖图(Dependency Graph)**：
   - webpack通过入口递归地构建依赖图
   - 包含应用所需的所有模块
   - 智能地识别无用代码

8. **代码分割(Code Splitting)**：
   - 将代码分割成不同的块，按需加载
   - 减小初始加载体积
   ```javascript
   module.exports = {
     optimization: {
       splitChunks: {
         chunks: 'all',
         cacheGroups: {
           vendors: {
             test: /[\\/]node_modules[\\/]/,
             name: 'vendors'
           }
         }
       }
     }
   };
   
   // 动态导入
   const Component = () => import('./Component');
   ```

**工作原理**：

1. **初始化阶段**：
   - 读取并合并配置参数
   - 创建Compiler实例
   - 注册内置插件

2. **构建阶段**：
   - 从入口文件开始递归解析模块依赖
   - 调用对应的loader处理模块
   - 将处理后的模块转换为AST
   - 分析AST，收集依赖
   - 生成模块依赖图谱

3. **生成阶段**：
   - 根据依赖图，组合各模块
   - 把各模块转换为对应的chunk
   - 将chunk转换为文件
   - 输出到指定目录

4. **插件工作机制**：
   - 基于事件/钩子系统
   - 在webpack生命周期的不同阶段执行
   ```javascript
   class MyPlugin {
     apply(compiler) {
       compiler.hooks.emit.tapAsync('MyPlugin', (compilation, callback) => {
         console.log('webpack构建即将输出文件...');
         callback();
       });
     }
   }
   ```

**实际应用示例**：

1. **基本开发配置**：
```javascript
// webpack.dev.js
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = {
  mode: 'development',
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'bundle.js'
  },
  devtool: 'inline-source-map',
  devServer: {
    static: './dist',
    hot: true
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader'
        }
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      }
    ]
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './src/index.html'
    })
  ]
};
```

2. **生产环境优化配置**：
```javascript
// webpack.prod.js
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  mode: 'production',
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].[contenthash].js',
    clean: true
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader'
        }
      },
      {
        test: /\.css$/,
        use: [MiniCssExtractPlugin.loader, 'css-loader']
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'images/[hash][ext][query]'
        }
      }
    ]
  },
  optimization: {
    minimizer: [
      new TerserPlugin(),
      new CssMinimizerPlugin()
    ],
    moduleIds: 'deterministic',
    runtimeChunk: 'single',
    splitChunks: {
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        }
      }
    }
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './src/index.html',
      minify: {
        removeComments: true,
        collapseWhitespace: true
      }
    }),
    new MiniCssExtractPlugin({
      filename: '[name].[contenthash].css'
    })
  ]
};
```

3. **自定义loader示例**：
```javascript
// 简单的markdown-loader
module.exports = function(source) {
  // 转换markdown为HTML(简化示例)
  const html = source
    .replace(/# (.*)/g, '<h1>$1</h1>')
    .replace(/## (.*)/g, '<h2>$1</h2>')
    .replace(/\*\*(.*)\*\*/g, '<strong>$1</strong>');
  
  // 返回JavaScript模块
  return `
    export default function() {
      const element = document.createElement('div');
      element.innerHTML = ${JSON.stringify(html)};
      return element;
    }
  `;
};
```

4. **自定义插件示例**：
```javascript
// 文件列表生成插件
class FileListPlugin {
  constructor(options) {
    this.options = options || { outputFile: 'filelist.md' };
  }
  
  apply(compiler) {
    // 在emit阶段插入钩子
    compiler.hooks.emit.tapAsync('FileListPlugin', (compilation, callback) => {
      // 创建文件列表
      let filelist = '# 文件列表\n\n';
      
      for (let filename in compilation.assets) {
        filelist += `- ${filename}\n`;
      }
      
      // 将文件列表作为新的资源插入到webpack构建中
      compilation.assets[this.options.outputFile] = {
        source: function() {
          return filelist;
        },
        size: function() {
          return filelist.length;
        }
      };
      
      callback();
    });
  }
}

// 使用插件
module.exports = {
  plugins: [
    new FileListPlugin({ outputFile: 'files.md' })
  ]
};
```

**优化和性能提升**：

1. **减少打包体积**：
   - Tree Shaking移除未使用代码
   - 代码分割与按需加载
   - 外部扩展(Externals)将库从包中分离

2. **提高打包速度**：
   - 缓存加快二次构建(cache-loader, babel-loader配置缓存)
   - 多线程/多进程打包(thread-loader, parallel-webpack)
   - 限制loader应用范围(include/exclude)

3. **开发体验优化**：
   - SourceMap配置优化
   - 热模块替换(HMR)提升开发效率
   - DevServer自定义配置

**Webpack 5新特性**：

1. **持久化缓存**：
   - 缓存生成的webpack模块和chunk
   - 改进构建速度

2. **模块联邦(Module Federation)**：
   - 允许多个webpack构建一起工作
   - 实现更灵活的微前端方案

3. **改进Tree Shaking**：
   - 更好的未使用模块/导出检测
   - 支持嵌套的Tree Shaking

4. **资源模块类型**：
   - 内置的asset模块，无需额外loader
   - 替代file-loader, url-loader等

5. **Node.js Polyfill移除**：
   - 不再自动polyfill Node.js核心模块
   - 减小构建体积，更符合Web标准

**考察重点**：
- Webpack核心概念的理解
- 配置文件的编写能力
- loader和plugin的工作原理
- 构建性能优化策略
- Webpack工作流程的深入理解

## Node.js

### 1. Node.js的事件循环机制与浏览器有何不同？

**答案**：
Node.js和浏览器都基于事件驱动的异步非阻塞I/O模型，都实现了事件循环机制，但在实现细节和执行流程上存在一些重要差异。

**Node.js事件循环概述**：

Node.js事件循环基于libuv库实现，它维护一个事件队列，当JavaScript代码执行I/O操作时，会将回调函数放入队列并立即返回，事件循环不断地从队列中取出回调执行，实现异步操作。

**事件循环阶段**：

Node.js的事件循环具有明确定义的6个阶段，按顺序执行：

1. **timers阶段**：执行setTimeout和setInterval的回调
2. **pending callbacks阶段**：执行某些系统操作的回调，如TCP错误
3. **idle, prepare阶段**：仅内部使用
4. **poll阶段**：检索新的I/O事件，执行I/O相关回调
5. **check阶段**：执行setImmediate的回调
6. **close callbacks阶段**：执行关闭事件的回调，如socket.on('close', ...)

每个阶段都有一个FIFO回调队列，当事件循环进入特定阶段，会执行该阶段队列中的回调，直到队列耗尽或达到系统上限，然后进入下一阶段。

**关键区别**：

1. **微任务执行时机**：
   - **浏览器**：微任务在当前宏任务执行完后立即执行，然后才进行UI渲染和开始下一个宏任务
   - **Node.js**：微任务在事件循环的各个阶段之间执行，不是在每个回调之后

2. **事件循环的优先级**：
   - **浏览器**：基于任务队列(宏任务队列和微任务队列)
   - **Node.js**：基于阶段和各阶段的优先级

3. **nextTick和微任务**：
   - **Node.js**：process.nextTick的优先级高于Promise微任务，会在当前操作完成后立即执行，而不是在事件循环的下一个阶段

4. **setImmediate与setTimeout(fn, 0)**：
   - **Node.js**：提供了setImmediate，在check阶段执行
   - setTimeout(fn, 0)和setImmediate的执行顺序在不同情况下可能不同

**Node.js事件循环详细过程**：

```
   ┌───────────────────────────┐
┌─>│           timers          │
│  └─────────────┬─────────────┘
│  ┌─────────────┴─────────────┐
│  │     pending callbacks     │
│  └─────────────┬─────────────┘
│  ┌─────────────┴─────────────┐
│  │       idle, prepare       │
│  └─────────────┬─────────────┘      ┌───────────────┐
│  ┌─────────────┴─────────────┐      │   incoming:   │
│  │           poll            │<─────┤  connections, │
│  └─────────────┬─────────────┘      │   data, etc.  │
│  ┌─────────────┴─────────────┐      └───────────────┘
│  │           check           │
│  └─────────────┬─────────────┘
│  ┌─────────────┴─────────────┐
└──┤      close callbacks      │
   └───────────────────────────┘
```

**举例说明**：

```javascript
// Node.js中微任务和宏任务的执行顺序示例
console.log('Start');

setTimeout(() => {
  console.log('setTimeout');
}, 0);

setImmediate(() => {
  console.log('setImmediate');
});

Promise.resolve().then(() => {
  console.log('Promise then');
});

process.nextTick(() => {
  console.log('nextTick');
});

console.log('End');

// 输出顺序:
// Start
// End
// nextTick        (process.nextTick优先级最高)
// Promise then    (微任务)
// setTimeout      (timers阶段)
// setImmediate    (check阶段)
```

**注意**：setTimeout(0)和setImmediate的执行顺序不是固定的，取决于各种因素，如系统负载。

**实际应用中的影响**：

1. **I/O密集型操作**：Node.js特别适合处理I/O密集型任务，因为事件循环针对I/O操作进行了优化。

2. **CPU密集型操作**：由于JavaScript是单线程的，CPU密集型任务会阻塞事件循环。在Node.js中应考虑使用工作线程(Worker Threads)或子进程。

3. **microtasks堆积**：大量的process.nextTick或Promise链可能会阻止事件循环进入下一个阶段，可能导致I/O饥饿，表现为应用响应缓慢。

**示例：定时器具体工作流程**：

```javascript
// 定时器处理示例
const fs = require('fs');

// 1. setTimeout设置一个100ms后执行的定时器
setTimeout(() => {
  console.log('1. setTimeout 100ms');
}, 100);

// 2. 读取文件操作
fs.readFile('some-file.txt', (err, data) => {
  // 4. 文件读取完成后，回调被加入到poll队列
  console.log('2. fs.readFile完成');
  
  // 5. 在I/O回调中设置的立即定时器
  setTimeout(() => {
    console.log('5. 嵌套的setTimeout');
  }, 0);
  
  // 6. 在I/O回调中设置的setImmediate
  setImmediate(() => {
    console.log('6. 嵌套的setImmediate');
  });
  
  // 7. 微任务会在当前阶段结束前执行
  Promise.resolve().then(() => {
    console.log('7. 嵌套的Promise.then微任务');
  });
});

// 3. 设置一个立即执行的定时器
setImmediate(() => {
  console.log('3. setImmediate');
});

// 在I/O操作内部，setImmediate总是先于setTimeout(fn, 0)执行
// 文件操作完成后的输出顺序:
// 2. fs.readFile完成
// 7. 嵌套的Promise.then微任务
// 6. 嵌套的setImmediate
// 5. 嵌套的setTimeout
```

**Node.js 11+的变化**：

从Node.js 11开始，Node.js开始向浏览器行为靠拢，微任务在每个阶段的回调之后立即执行，而不是在阶段之间执行，这使得Node.js和浏览器的行为更加一致。

**实用建议**：

1. 使用process.nextTick处理需要"尽快但不是立即"执行的操作
2. 使用setImmediate处理需要在当前事件循环阶段完成后执行的操作
3. 避免在回调中使用嵌套的process.nextTick，可能会阻断I/O操作
4. 大型计算应分解为小块并使用setImmediate调度，以避免阻塞事件循环

**考察重点**：
- Node.js事件循环的各个阶段及其特点
- Node.js与浏览器事件循环的区别
- 微任务(nextTick, Promise)和宏任务(setTimeout, setImmediate)在Node.js中的优先级
- 事件循环对应用性能的影响
- 如何针对Node.js的事件循环特性优化代码

### 2. Node.js中的模块机制是如何工作的？对比CommonJS、ES Module和AMD

**答案**：
Node.js的模块系统允许开发者将代码拆分为独立的功能块，提高代码的可维护性和复用性。Node.js支持多种模块系统，主要包括CommonJS(Node.js原生)和ES Modules(在较新版本引入)。

**Node.js模块机制的基本概念**：

1. **模块封装**：每个模块在运行时都被包装在一个函数中，提供隔离的作用域
2. **模块缓存**：首次加载后的模块会被缓存，确保多次require同一模块时获得相同实例
3. **循环依赖处理**：Node.js允许模块间循环依赖，但由于缓存机制和加载顺序，要小心使用

**CommonJS模块(Node.js原生模块系统)**：

CommonJS是Node.js最初采用的模块系统，特点是同步加载，使用require()导入，module.exports或exports导出。

**基本用法**：

```javascript
// math.js - 导出模块
function add(a, b) {
  return a + b;
}

function subtract(a, b) {
  return a - b;
}

// 导出方式1：使用module.exports整体导出
module.exports = {
  add,
  subtract
};

// 导出方式2：使用exports逐个导出
exports.multiply = function(a, b) {
  return a * b;
};

// ⚠️ 不能直接赋值给exports
// exports = { add, subtract }; // 这样不起作用!
```

```javascript
// app.js - 导入模块
const math = require('./math');
console.log(math.add(5, 3)); // 输出 8

// 解构导入
const { subtract } = require('./math');
console.log(subtract(10, 4)); // 输出 6
```

**CommonJS加载过程**：

1. **解析**：确定模块的绝对路径
2. **加载**：如果模块未被加载，则加载并执行模块代码
3. **缓存**：缓存模块导出，后续require直接返回缓存结果
4. **返回导出**：返回module.exports对象

**CommonJS模块包装机制**：

Node.js会在执行模块代码前将其包装在函数中：

```javascript
(function(exports, require, module, __filename, __dirname) {
  // 模块代码实际在这里
});
```

这提供了模块级的作用域以及exports、require等变量。

**ES Modules(ECMAScript模块)**：

ES Modules是JavaScript官方的标准模块系统，在浏览器和Node.js(版本12+)中均可使用。特点是静态导入导出，支持异步加载。

**在Node.js中使用ES Modules**：

方法1：使用.mjs扩展名
方法2：在package.json中设置`"type": "module"`
方法3：在最近的package.json中设置`"type": "module"`

**基本用法**：

```javascript
// mathUtils.mjs - 导出模块
// 命名导出
export function add(a, b) {
  return a + b;
}

export function subtract(a, b) {
  return a - b;
}

// 默认导出
export default function multiply(a, b) {
  return a * b;
}
```

```javascript
// app.mjs - 导入模块
// 导入命名导出
import { add, subtract } from './mathUtils.mjs';
console.log(add(5, 3)); // 输出 8

// 导入默认导出
import multiply from './mathUtils.mjs';
console.log(multiply(4, 5)); // 输出 20

// 重命名导入
import { add as sum } from './mathUtils.mjs';
console.log(sum(10, 5)); // 输出 15

// 导入所有导出为一个对象
import * as math from './mathUtils.mjs';
console.log(math.subtract(10, 3)); // 输出 7
```

**ES Modules加载过程**：

1. **构建**：查找、下载并解析所有模块到模块记录
2. **实例化**：分配内存，将所有导出映射到内存中(但未填充值)
3. **执行**：运行代码，填充内存中的导出值

**AMD模块(异步模块定义)**：

主要用于浏览器环境，特点是异步加载，不阻塞页面渲染。RequireJS是常用实现。

```javascript
// 定义模块
define('myModule', ['dependency1', 'dependency2'], function(dep1, dep2) {
  return {
    doSomething: function() {
      // 使用dep1和dep2
    }
  };
});

// 使用模块
require(['myModule'], function(myModule) {
  myModule.doSomething();
});
```

**三种模块系统对比**：

| 特性 | CommonJS | ES Modules | AMD |
|------|----------|------------|-----|
| **加载方式** | 同步加载 | 静态加载(编译时) | 异步加载 |
| **导入语法** | `require()` | `import` | `require([])` |
| **导出语法** | `module.exports`/`exports` | `export`/`export default` | 返回值或对象 |
| **主要环境** | Node.js服务端 | 浏览器和现代Node.js | 浏览器(历史) |
| **循环依赖** | 部分解决，返回未完成实例 | 完全支持，链接到导出引用 | 支持 |
| **动态导入** | 支持 | 通过`import()`支持 | 原生支持 |
| **加载顺序** | 运行时才确定 | 编译时确定，执行前完成分析 | 运行时异步 |
| **静态分析** | 不支持(运行时解析) | 支持(编译时解析) | 不支持 |
| **Tree Shaking** | 不支持 | 支持(因静态分析) | 不支持 |

**Node.js中的模块解析算法**：

当使用require加载模块时，Node.js按以下顺序解析模块路径：

1. **核心模块**：如fs、path等内置模块
2. **文件/目录模块**：
   - 若路径以'/'、'./'或'../'开头，尝试加载确切文件
   - 尝试添加.js、.json、.node扩展名
   - 若路径是目录，查找package.json中的main字段
   - 尝试加载index.js、index.json或index.node
3. **node_modules模块**：
   - 从当前目录的node_modules查找
   - 继续向上级目录的node_modules查找，直到根目录

**实际使用中的要点**：

1. **动态导入**：
   - **CommonJS**：支持动态路径，`const module = require(`./${name}`);`
   - **ES Modules**：使用动态import()，`const module = await import(`./${name}.js`);`
   
2. **循环依赖处理**：
   ```javascript
   // a.js
   console.log('a模块开始执行');
   exports.done = false;
   const b = require('./b.js');
   console.log('在a模块中，b.done =', b.done);
   exports.done = true;
   console.log('a模块执行完毕');
   
   // b.js
   console.log('b模块开始执行');
   exports.done = false;
   const a = require('./a.js');
   console.log('在b模块中，a.done =', a.done);
   exports.done = true;
   console.log('b模块执行完毕');
   
   // main.js
   console.log('main开始执行');
   const a = require('./a.js');
   console.log('在main中，a.done =', a.done);
   
   // 输出结果:
   // main开始执行
   // a模块开始执行
   // b模块开始执行
   // 在b模块中，a.done = false
   // b模块执行完毕
   // 在a模块中，b.done = true
   // a模块执行完毕
   // 在main中，a.done = true
   ```
   
3. **混合使用两种模块系统**：

   Node.js允许在一个项目中混合使用CommonJS和ES Modules，但需注意：
   
   - ESM可以导入CJS模块，但只能访问其module.exports对象
   - CJS可以通过动态`import()`导入ESM模块，但需要使用异步语法

   ```javascript
   // ESM导入CJS模块
   import cjsModule from './commonjs-module.js';
   
   // CJS导入ESM模块
   async function loadESM() {
     const esmModule = await import('./es-module.mjs');
     esmModule.default(); // 访问默认导出
   }
   ```

4. **区分默认导出和命名导出**：
   - ES Modules区分默认导出和命名导出
   - CommonJS只有一个导出对象(module.exports)

5. **包导入导出策略**：
   - 使用package.json的exports字段定义包的公共API
   ```json
   // package.json
   {
     "name": "my-package",
     "exports": {
       ".": "./index.js",
       "./utils": "./src/utils.js",
       "./utils/*": "./src/utils/*.js"
     }
   }
   ```

**Node.js中的模块缓存**：

Node.js会缓存模块，可以通过`require.cache`查看或操作缓存：

```javascript
// 查看缓存
console.log(Object.keys(require.cache));

// 删除缓存，强制重新加载模块
delete require.cache[require.resolve('./someModule')];
const freshModule = require('./someModule');
```

**实际应用例子**：

1. **创建可插拔模块系统**：

```javascript
// plugins/index.js
const fs = require('fs');
const path = require('path');

// 动态加载目录中的所有插件
function loadPlugins(pluginsDir) {
  const plugins = {};
  
  fs.readdirSync(pluginsDir)
    .filter(file => file.endsWith('.js'))
    .forEach(file => {
      const name = path.basename(file, '.js');
      const pluginPath = path.join(pluginsDir, file);
      plugins[name] = require(pluginPath);
    });
    
  return plugins;
}

module.exports = loadPlugins(path.join(__dirname));
```

2. **使用ES Modules的顶级await**：

```javascript
// 在ES Module中使用顶级await
// config.mjs
import { readFile } from 'fs/promises';

export const config = JSON.parse(
  await readFile(new URL('./config.json', import.meta.url))
);

// 使用配置
// app.mjs
import { config } from './config.mjs';
console.log('配置已加载:', config);
```

**考察重点**：
- CommonJS和ES Modules的基本语法和使用
- 两种模块系统的加载机制差异
- 模块缓存与模块解析算法
- 循环依赖处理方式
- 动态导入能力
- 混合模块系统的使用注意事项

### 3. Node.js中如何实现并发处理和利用多核CPU？

**答案**：
Node.js是单线程的，但可以通过多种方式实现并发处理和充分利用多核CPU资源，主要有异步I/O、集群模式、工作线程和子进程等方式。

**1. 异步I/O模型**：

Node.js的基础并发模型是基于事件循环的非阻塞I/O，可以同时处理多个请求而不会阻塞线程。

```javascript
// 异步I/O示例
const fs = require('fs');

// 这不会阻塞事件循环
fs.readFile('largefile.txt', (err, data) => {
  if (err) throw err;
  console.log('文件读取完成');
});

// 读取文件时，代码会继续执行
console.log('继续处理其他任务');
```

**优点**：
- 简单高效，不需要额外线程开销
- 适合I/O密集型操作

**缺点**：
- 不能利用多核CPU
- CPU密集型任务会阻塞事件循环

**2. Cluster模块（集群）**：

Node.js的cluster模块允许创建共享服务器端口的子进程，使应用能够在多个核心上提供服务。

```javascript
// cluster.js
const cluster = require('cluster');
const http = require('http');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
  console.log(`主进程 ${process.pid} 正在运行`);

  // 衍生工作进程
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  cluster.on('exit', (worker, code, signal) => {
    console.log(`工作进程 ${worker.process.pid} 已退出`);
    // 可以在这里重新启动工作进程
    cluster.fork();
  });
} else {
  // 工作进程共享同一个TCP连接
  http.createServer((req, res) => {
    res.writeHead(200);
    res.end(`你好世界，来自工作进程 ${process.pid}\n`);
  }).listen(8000);

  console.log(`工作进程 ${process.pid} 已启动`);
}
```

**Cluster模块的工作原理**：
- 主进程创建监听socket并分发连接
- Round-Robin负载均衡（Windows除外）或操作系统负载均衡
- 工作进程可以相互通信

**优点**：
- 简单实现多核利用
- 共享同一端口
- 进程间隔离，一个崩溃不影响其他

**缺点**：
- 状态不共享，需要额外存储机制
- 进程间通信成本较高
- 适合无状态应用

**3. Worker Threads（工作线程）**：

Node.js 10+引入的工作线程模块，提供真正的多线程能力，共享内存，适合CPU密集型任务。

```javascript
// worker_threads_example.js
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');

// 计算斐波那契数列（CPU密集型）
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

if (isMainThread) {
  // 这是主线程
  const threads = new Set();
  console.time('使用工作线程计算');
  
  // 创建多个工作线程
  for (let i = 0; i < 4; i++) {
    const worker = new Worker(__filename, { 
      workerData: { start: 35 + i }
    });
    
    threads.add(worker);
    
    worker.on('message', (result) => {
      console.log(`斐波那契(${result.n}) = ${result.value}`);
    });
    
    worker.on('error', (err) => {
      console.error(err);
    });
    
    worker.on('exit', () => {
      threads.delete(worker);
      if (threads.size === 0) {
        console.timeEnd('使用工作线程计算');
      }
    });
  }
} else {
  // 这是工作线程
  const n = workerData.start;
  const result = fibonacci(n);
  parentPort.postMessage({ n, value: result });
}
```

**共享内存的使用**：

```javascript
// shared_memory_example.js
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const { SharedArrayBuffer, Atomics } = require('shared_buffer');

if (isMainThread) {
  // 创建共享内存
  const buffer = new SharedArrayBuffer(4);
  const view = new Int32Array(buffer);
  
  // 初始化为0
  view[0] = 0;
  
  // 创建工作线程并共享内存
  const worker = new Worker(__filename, { workerData: { buffer } });
  
  worker.on('message', (msg) => {
    console.log('来自工作线程的消息:', msg);
    console.log('当前共享内存值:', view[0]); // 应为100
  });
} else {
  // 工作线程中
  const { buffer } = workerData;
  const view = new Int32Array(buffer);
  
  // 使用Atomics进行原子操作，确保多线程安全
  Atomics.add(view, 0, 100);
  
  parentPort.postMessage('共享内存值已更新');
}
```

**工作线程的优点**：
- 线程之间可以高效共享内存
- 适合CPU密集型计算
- 避免进程创建开销

**工作线程的缺点**：
- 共享内存带来的复杂性和并发问题
- 需注意死锁和竞态条件
- Node.js 10以后才支持

**4. Child Process（子进程）**：

使用child_process模块可以创建多个独立进程来执行任务。

```javascript
// child_process_example.js
const { spawn, exec, fork } = require('child_process');

// 使用spawn执行命令（推荐用于长时间运行的进程）
const ls = spawn('ls', ['-la']);

ls.stdout.on('data', (data) => {
  console.log(`stdout: ${data}`);
});

ls.stderr.on('data', (data) => {
  console.error(`stderr: ${data}`);
});

ls.on('close', (code) => {
  console.log(`子进程退出，退出码 ${code}`);
});

// 使用exec执行命令（便于获取输出）
exec('find . -type f | wc -l', (error, stdout, stderr) => {
  if (error) {
    console.error(`执行出错: ${error}`);
    return;
  }
  console.log(`当前目录文件数量: ${stdout}`);
});

// 使用fork创建Node.js子进程（推荐用于执行Node.js代码）
const forked = fork('child.js');

forked.on('message', (msg) => {
  console.log('来自子进程的消息:', msg);
});

// 发送消息到子进程
forked.send({ hello: 'world' });
```

子进程文件示例：
```javascript
// child.js
process.on('message', (msg) => {
  console.log('收到父进程消息:', msg);
  // 执行一些计算...
  process.send({ result: 'task completed' });
});
```

**子进程的优点**：
- 完全隔离的内存空间
- 可执行不同语言的程序
- 稳定性高，一个崩溃不影响其他

**子进程的缺点**：
- 进程创建的开销较大
- 通信依赖序列化，效率低于工作线程
- 内存占用较高

**5. 实际应用场景选择**：

| 方案 | 适用场景 | 优势 | 劣势 |
|------|---------|------|------|
| **异步I/O** | I/O密集型，如Web API | 简单高效，开销小 | CPU密集型任务会阻塞 |
| **Cluster** | Web服务器扩展 | 简单利用多核，进程独立 | 状态不共享，需存储方案 |
| **Worker Threads** | 数据处理，图像处理 | 共享内存高效，开销小 | 复杂并发问题，相对新特性 |
| **Child Process** | 执行外部命令，重计算 | 隔离稳定，语言无关 | 通信开销大，占用资源多 |

**6. 结合使用的最佳实践**：

1. **Web服务的分层并发**：
   - 使用Cluster创建与CPU核心数相当的进程
   - 每个工作进程使用异步I/O处理请求
   - CPU密集任务交给Worker Threads
   - 长时间运行或其他语言的任务使用Child Process

```javascript
// 综合示例
const cluster = require('cluster');
const http = require('http');
const { Worker } = require('worker_threads');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
  console.log(`主进程 ${process.pid} 正在运行`);

  // 创建工作进程
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  cluster.on('exit', (worker) => {
    console.log(`工作进程 ${worker.process.pid} 退出`);
    cluster.fork(); // 替换退出的工作进程
  });
} else {
  // 工作进程代码
  http.createServer(async (req, res) => {
    if (req.url === '/compute') {
      // CPU密集型任务使用工作线程
      const worker = new Worker('./compute_worker.js');
      
      worker.on('message', (result) => {
        res.writeHead(200);
        res.end(`计算结果: ${result}`);
      });
      
      worker.postMessage({ type: 'compute', data: 1000000 });
    } else if (req.url === '/io') {
      // I/O操作使用异步API
      const data = await new Promise(resolve => {
        setTimeout(() => resolve('I/O操作完成'), 100);
      });
      
      res.writeHead(200);
      res.end(data);
    } else {
      res.writeHead(200);
      res.end('Hello World');
    }
  }).listen(8000);

  console.log(`工作进程 ${process.pid} 已启动`);
}
```

**7. 监控和负载均衡**：

```javascript
// 工作进程监控示例
const cluster = require('cluster');
const os = require('os');

if (cluster.isMaster) {
  const numCPUs = os.cpus().length;
  
  // 存储工作进程负载信息
  const workers = {};
  
  // 创建工作进程
  for (let i = 0; i < numCPUs; i++) {
    const worker = cluster.fork();
    workers[worker.id] = { id: worker.id, load: 0 };
  }
  
  // 接收工作进程负载汇报
  cluster.on('message', (worker, message) => {
    if (message.type === 'load_report') {
      workers[worker.id].load = message.load;
      console.log(`工作进程 ${worker.id} 当前负载: ${message.load}`);
    }
  });
  
  // 定期检查并平衡负载
  setInterval(() => {
    const workerLoads = Object.values(workers);
    const totalLoad = workerLoads.reduce((sum, w) => sum + w.load, 0);
    const avgLoad = totalLoad / workerLoads.length;
    
    console.log(`系统平均负载: ${avgLoad.toFixed(2)}`);
    
    // 负载过高的工作进程可以被重启
    for (const id in workers) {
      if (workers[id].load > avgLoad * 1.5) {
        console.log(`重启高负载工作进程 ${id}`);
        cluster.workers[id].kill();
        const newWorker = cluster.fork();
        workers[newWorker.id] = { id: newWorker.id, load: 0 };
        delete workers[id];
      }
    }
  }, 10000);
} else {
  // 工作进程代码...
  
  // 定期汇报负载
  setInterval(() => {
    // 简单负载指标：当前请求数、内存使用等
    const load = process._getActiveRequests().length;
    process.send({ type: 'load_report', load });
  }, 5000);
  
  // 处理请求...
}
```

**8. 优化并发处理的实用技巧**：

1. **任务队列**：使用队列来控制并发任务数量
   ```javascript
   // 简单任务队列
   class TaskQueue {
     constructor(concurrency) {
       this.concurrency = concurrency;
       this.running = 0;
       this.queue = [];
     }
     
     async add(task) {
       return new Promise((resolve, reject) => {
         this.queue.push({ task, resolve, reject });
         this.run();
       });
     }
     
     async run() {
       if (this.running >= this.concurrency || this.queue.length === 0) {
         return;
       }
       
       this.running++;
       const { task, resolve, reject } = this.queue.shift();
       
       try {
         const result = await task();
         resolve(result);
       } catch (err) {
         reject(err);
       } finally {
         this.running--;
         this.run();
       }
     }
   }
   
   // 使用
   const queue = new TaskQueue(4); // 最多同时执行4个任务
   
   for (let i = 0; i < 100; i++) {
     queue.add(() => someExpensiveOperation(i));
   }
   ```

2. **Worker Pool**：创建线程池或进程池
   ```javascript
   // worker_pool.js
   const { Worker } = require('worker_threads');
   const path = require('path');
   
   class WorkerPool {
     constructor(size, workerScript) {
       this.size = size;
       this.workerScript = workerScript;
       this.workers = [];
       this.queue = [];
       this._init();
     }
     
     _init() {
       for (let i = 0; i < this.size; i++) {
         this.createWorker();
       }
     }
     
     createWorker() {
       const worker = new Worker(this.workerScript);
       
       worker.on('message', (result) => {
         // 处理任务完成
         worker.taskResolve(result);
         
         // 处理队列中的下一个任务
         if (this.queue.length > 0) {
           const { task, resolve, reject } = this.queue.shift();
           this.runTask(worker, task, resolve, reject);
         } else {
           worker.isIdle = true;
         }
       });
       
       worker.on('error', (err) => {
         if (worker.taskReject) {
           worker.taskReject(err);
         }
         this.workers = this.workers.filter(w => w !== worker);
         this.createWorker(); // 替换错误的工作线程
       });
       
       worker.isIdle = true;
       this.workers.push(worker);
     }
     
     runTask(worker, task, resolve, reject) {
       worker.isIdle = false;
       worker.taskResolve = resolve;
       worker.taskReject = reject;
       worker.postMessage(task);
     }
     
     execute(task) {
       return new Promise((resolve, reject) => {
         const idleWorker = this.workers.find(w => w.isIdle);
         
         if (idleWorker) {
           this.runTask(idleWorker, task, resolve, reject);
         } else {
           this.queue.push({ task, resolve, reject });
         }
       });
     }
     
     close() {
       for (const worker of this.workers) {
         worker.terminate();
       }
     }
   }
   
   module.exports = WorkerPool;
   ```

3. **优化CPU和内存使用**：
   - 避免在关键路径上使用同步API
   - 使用Stream处理大型数据集
   - 合理设置进程/线程数，不超过CPU核心数+1或2
   - 监控内存使用，设置合理的老生代内存限制(--max-old-space-size)

**考察重点**：
- 理解Node.js单线程模型及其局限性
- 熟悉不同的并发处理机制及适用场景
- 能够根据需求选择恰当的并发策略
- 了解不同并发方案的通信机制
- 掌握负载均衡和错误恢复策略

### 4. Express和Koa框架有什么区别？如何实现一个中间件？

**答案**：
Express和Koa都是基于Node.js的流行Web框架，由同一团队开发，但设计理念和API设计上有显著差异。

**1. Express和Koa的主要区别**：

| 特性 | Express | Koa |
|------|---------|-----|
| **发布时间** | 2010年，较早成熟 | 2013年，由Express团队开发 |
| **中间件模型** | 基于回调函数(callback) | 基于async/await |
| **错误处理** | try-catch和next(err)传递 | try-catch原生支持 |
| **内置功能** | 路由、视图渲染等功能内置 | 极简核心，功能需额外安装 |
| **请求处理** | 线性流程，middleware之间层层调用 | 洋葱模型，先入后出，可追踪完整请求周期 |
| **社区生态** | 更大更成熟的生态 | 较小但专注于现代JS特性 |

**2. Express框架特点**：

Express是一个极简、灵活的Node.js Web应用框架，为Web和移动应用提供一系列强大的功能。

```javascript
// Express基本用法
const express = require('express');
const app = express();

// 中间件使用
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 路由定义
app.get('/', (req, res) => {
  res.send('Hello World!');
});

app.post('/users', (req, res) => {
  // 处理创建用户
  res.status(201).json({ message: 'User created' });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).send('Something broke!');
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
```

**Express中间件流程**：
Express中间件是线性执行的。当调用next()时，控制权传递给下一个中间件，依此类推，直到请求完成处理。

```
Client Request → Middleware 1 → Middleware 2 → Route Handler → Response
```

**3. Koa框架特点**：

Koa是一个更现代的Web框架，由Express原班人马设计，旨在成为更小、更富表现力和更健壮的基础框架。

```javascript
// Koa基本用法
const Koa = require('koa');
const Router = require('@koa/router');
const bodyParser = require('koa-bodyparser');

const app = new Koa();
const router = new Router();

// 中间件使用
app.use(bodyParser());

// 全局错误处理
app.use(async (ctx, next) => {
  try {
    await next();
  } catch (err) {
    ctx.status = err.status || 500;
    ctx.body = { error: err.message };
    ctx.app.emit('error', err, ctx);
  }
});

// 路由定义
router.get('/', (ctx) => {
  ctx.body = 'Hello World';
});

router.post('/users', (ctx) => {
  // 处理创建用户
  ctx.status = 201;
  ctx.body = { message: 'User created' };
});

app.use(router.routes()).use(router.allowedMethods());

app.on('error', (err, ctx) => {
  console.error('server error', err);
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
```

**Koa中间件流程（洋葱模型）**：
Koa的洋葱模型中间件执行顺序：从外到内再从内到外。
```
               ┌─────────────────────────────────────┐
               │                                     │
               │            Middleware 1             │
               │     ┌───────────────────────┐      │
               │     │                       │      │
 Request  ─────┼────►│      Middleware 2     │      │
               │     │   ┌───────────────┐   │      │
               │     │   │               │   │      │
               │     │   │  Route Handler│   │      │
               │     │   │               │   │      │
               │     │   └───────┬───────┘   │      │
               │     │           │           │      │
 Response ◄────┼─────┼───────────┼───────────┼──────┼─── 
               │     │           ▼           │      │
               │     └───────────────────────┘      │
               │                                     │
               └─────────────────────────────────────┘
```

**在Koa中，中间件是这样执行的**：

```javascript
app.use(async (ctx, next) => {
  console.log('Middleware 1 - 进入');
  await next(); // 控制权传递给下一个中间件
  console.log('Middleware 1 - 退出'); // 下游中间件执行完后，控制权返回
});

app.use(async (ctx, next) => {
  console.log('Middleware 2 - 进入');
  await next();
  console.log('Middleware 2 - 退出');
});

// 请求处理中的输出：
// Middleware 1 - 进入
// Middleware 2 - 进入
// Middleware 2 - 退出
// Middleware 1 - 退出
```

**4. 中间件实现原理**：

**Express中间件实现**：

Express中间件是通过函数链调用实现的：

```javascript
// Express中间件实现原理
function express() {
  const middleware = [];
  
  const app = function(req, res) {
    let i = 0;
    
    function next() {
      const fn = middleware[i++];
      if (!fn) return;
      
      try {
        fn(req, res, next);
      } catch (err) {
        // 查找错误处理中间件
        while (i < middleware.length) {
          const errorHandler = middleware[i++];
          if (errorHandler.length === 4) {
            return errorHandler(err, req, res, next);
          }
        }
        throw err;
      }
    }
    
    next();
  };
  
  app.use = function(fn) {
    middleware.push(fn);
    return app;
  };
  
  return app;
}
```

**Koa中间件实现**：

Koa通过组合(compose)函数实现洋葱模型：

```javascript
// Koa中间件实现原理
function compose(middleware) {
  return function(context, next) {
    let index = -1;
    return dispatch(0);
    
    function dispatch(i) {
      if (i <= index) return Promise.reject(new Error('next() called multiple times'));
      index = i;
      let fn = middleware[i];
      if (i === middleware.length) fn = next;
      if (!fn) return Promise.resolve();
      try {
        return Promise.resolve(fn(context, dispatch.bind(null, i + 1)));
      } catch (err) {
        return Promise.reject(err);
      }
    }
  };
}

// 用法
const fn = compose([middleware1, middleware2, middleware3]);
fn(ctx).then(() => {
  console.log('所有中间件执行完毕');
}).catch(err => {
  console.error('中间件执行错误', err);
});
```

**5. 实现一个自定义中间件**：

**Express中间件示例**：

```javascript
// 1. 日志中间件
function logger(options) {
  const opts = options || { format: ':method :url :status :response-time ms' };
  
  return function(req, res, next) {
    const start = Date.now();
    const originalEnd = res.end;
    
    res.end = function(...args) {
      const duration = Date.now() - start;
      let log = opts.format
        .replace(':method', req.method)
        .replace(':url', req.originalUrl || req.url)
        .replace(':status', res.statusCode)
        .replace(':response-time', duration);
      
      console.log(log);
      originalEnd.apply(res, args);
    };
    
    next();
  };
}

// 使用自定义中间件
app.use(logger());

// 2. 认证中间件
function authenticate(options) {
  return function(req, res, next) {
    const token = req.headers.authorization;
    
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }
    
    try {
      // 验证token (实际场景会用JWT等库)
      const decoded = verifyToken(token);
      req.user = decoded;
      next();
    } catch (err) {
      res.status(401).json({ error: 'Invalid token' });
    }
  };
}

// 使用认证中间件保护路由
app.get('/profile', authenticate(), (req, res) => {
  res.json({ user: req.user });
});
```

**Koa中间件示例**：

```javascript
// 1. 日志中间件
function logger(options = {}) {
  return async (ctx, next) => {
    const start = Date.now();
    
    try {
      await next();
    } finally {
      const ms = Date.now() - start;
      const logFormat = options.format || ':method :url :status :time ms';
      
      const log = logFormat
        .replace(':method', ctx.method)
        .replace(':url', ctx.url)
        .replace(':status', ctx.status)
        .replace(':time', ms);
      
      console.log(log);
    }
  };
}

// 使用自定义中间件
app.use(logger());

// 2. 认证中间件
function authenticate(options = {}) {
  return async (ctx, next) => {
    const token = ctx.headers.authorization;
    
    if (!token) {
      ctx.status = 401;
      ctx.body = { error: 'No token provided' };
      return;
    }
    
    try {
      // 验证token
      const decoded = verifyToken(token);
      ctx.state.user = decoded;
      await next();
    } catch (err) {
      ctx.status = 401;
      ctx.body = { error: 'Invalid token' };
    }
  };
}

// 使用认证中间件保护路由
router.get('/profile', authenticate(), (ctx) => {
  ctx.body = { user: ctx.state.user };
});
```

**6. Express和Koa中间件示例比较**：

**例子：处理跨域(CORS)中间件**

Express版本：
```javascript
function cors(options = {}) {
  const defaultOptions = {
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: false,
    maxAge: 86400 // 24小时
  };
  
  const opts = { ...defaultOptions, ...options };
  
  return function(req, res, next) {
    res.setHeader('Access-Control-Allow-Origin', opts.origin);
    
    // 预检请求
    if (req.method === 'OPTIONS') {
      res.setHeader('Access-Control-Allow-Methods', opts.methods);
      res.setHeader('Access-Control-Max-Age', opts.maxAge.toString());
      
      if (opts.credentials) {
        res.setHeader('Access-Control-Allow-Credentials', 'true');
      }
      
      if (req.headers['access-control-request-headers']) {
        res.setHeader('Access-Control-Allow-Headers', 
                      req.headers['access-control-request-headers']);
      }
      
      return res.status(204).end();
    }
    
    next();
  };
}

// 使用
app.use(cors({
  origin: 'https://example.com',
  credentials: true
}));
```

Koa版本：
```javascript
function cors(options = {}) {
  const defaultOptions = {
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: false,
    maxAge: 86400 // 24小时
  };
  
  const opts = { ...defaultOptions, ...options };
  
  return async (ctx, next) => {
    ctx.set('Access-Control-Allow-Origin', opts.origin);
    
    // 预检请求
    if (ctx.method === 'OPTIONS') {
      ctx.set('Access-Control-Allow-Methods', opts.methods);
      ctx.set('Access-Control-Max-Age', opts.maxAge.toString());
      
      if (opts.credentials) {
        ctx.set('Access-Control-Allow-Credentials', 'true');
      }
      
      if (ctx.get('Access-Control-Request-Headers')) {
        ctx.set('Access-Control-Allow-Headers', 
               ctx.get('Access-Control-Request-Headers'));
      }
      
      ctx.status = 204;
      return;
    }
    
    await next();
  };
}

// 使用
app.use(cors({
  origin: 'https://example.com',
  credentials: true
}));
```

**7. 何时选择Express或Koa**：

**选择Express的情况**：
- 需要快速开发具备内置功能的应用
- 项目需要广泛的中间件生态支持
- 团队更熟悉回调函数风格的API
- 开发传统的、功能完备的REST API

**选择Koa的情况**：
- 使用现代JavaScript(async/await)语法
- 需要更优雅的错误处理方式
- 构建自定义的、灵活的应用架构
- 需要更精细的请求控制，如流式处理

**8. 实际项目中的最佳实践**：

**Express最佳实践**：
- 使用express-validator进行请求验证
- 将路由拆分到单独的文件
- 使用helmet增强安全性
- 遵循RESTful API设计原则
- 合理组织中间件的顺序，将全局中间件放在最前面

```javascript
// express项目结构示例
/project
  /controllers
    users.js
    products.js
  /middlewares
    auth.js
    error.js
  /routes
    users.js
    products.js
  /services
    user-service.js
  app.js
  server.js
```

**Koa最佳实践**：
- 使用koa-router处理路由
- 使用koa-bodyparser解析请求体
- 设置全局错误处理中间件
- 利用ctx.state在中间件间共享状态
- 使用async/await处理异步操作

```javascript
// koa项目结构示例
/project
  /controllers
    users.js
    products.js
  /middlewares
    auth.js
    error.js
  /routes
    users.js
    products.js
  /services
    user-service.js
  app.js
  server.js
```

**考察重点**：
- 理解Express和Koa的设计理念和核心区别
- 掌握两种框架的中间件机制与实现原理
- 能够根据项目需求选择合适的框架
- 编写高质量的自定义中间件
- 理解洋葱模型的执行流程及其优势

### 5. Node.js如何处理数据库操作？介绍ORM和常见数据库实践

**答案**：
Node.js可以通过各种驱动程序和ORM框架与几乎所有主流数据库进行交互，包括关系型数据库(如MySQL、PostgreSQL)和NoSQL数据库(如MongoDB、Redis)。

**1. 数据库连接方式**：

Node.js提供了多种连接数据库的方式，主要分为原生驱动和ORM两大类：

**原生数据库驱动示例**：

```javascript
// MySQL原生驱动示例
const mysql = require('mysql2/promise');

async function connectDB() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'password',
    database: 'myapp'
  });
  
  try {
    // 执行SQL查询
    const [rows, fields] = await connection.execute(
      'SELECT * FROM users WHERE id = ?', 
      [userId]
    );
    return rows;
  } finally {
    // 关闭连接
    await connection.end();
  }
}

// MongoDB原生驱动示例
const { MongoClient } = require('mongodb');

async function connectMongo() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    const db = client.db('myapp');
    const users = db.collection('users');
    
    // 查询文档
    const user = await users.findOne({ _id: userId });
    return user;
  } finally {
    await client.close();
  }
}
```

**2. ORM (对象关系映射)**：

ORM是一种编程技术，将数据库中的记录映射到面向对象编程语言中的对象，简化数据库交互并提供更高级的抽象。

**ORM的优势**：

- 使用面向对象的方式操作数据库
- 减少SQL注入风险
- 数据验证和类型转换
- 数据库迁移和版本控制
- 跨数据库支持

**Node.js中常用的ORM框架**：

1. **Sequelize** - 支持MySQL、PostgreSQL、SQLite等关系型数据库：

```javascript
// Sequelize示例
const { Sequelize, DataTypes } = require('sequelize');

// 创建连接
const sequelize = new Sequelize('myapp', 'username', 'password', {
  host: 'localhost',
  dialect: 'mysql',
  pool: {
    max: 5,
    min: 0,
    idle: 10000
  }
});

// 定义模型
const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  username: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  timestamps: true,
  paranoid: true // 软删除
});

// 使用模型
async function createUser(userData) {
  try {
    const user = await User.create(userData);
    return user;
  } catch (error) {
    console.error('创建用户失败:', error);
    throw error;
  }
}

async function findUserByEmail(email) {
  try {
    const user = await User.findOne({
      where: { email },
      attributes: ['id', 'username', 'email', 'isActive'] // 选择特定字段
    });
    return user;
  } catch (error) {
    console.error('查询用户失败:', error);
    throw error;
  }
}

// 高级查询示例
async function findActiveUsers(page = 1, limit = 10) {
  try {
    const offset = (page - 1) * limit;
    
    const { count, rows } = await User.findAndCountAll({
      where: { isActive: true },
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
    
    return {
      total: count,
      users: rows,
      page,
      totalPages: Math.ceil(count / limit)
    };
  } catch (error) {
    console.error('查询用户列表失败:', error);
    throw error;
  }
}
```

2. **TypeORM** - 支持TypeScript，适用于多种数据库：

```typescript
// TypeORM示例 (使用TypeScript)
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, BaseEntity } from 'typeorm';

@Entity('users')
export class User extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;
  
  @Column({ unique: true })
  username: string;
  
  @Column()
  email: string;
  
  @Column({ select: false }) // 默认查询不返回密码
  password: string;
  
  @Column({ default: true })
  isActive: boolean;
  
  @CreateDateColumn()
  createdAt: Date;
  
  @UpdateDateColumn()
  updatedAt: Date;
}

// 使用Repository模式
import { getRepository } from 'typeorm';

async function getUserProfile(userId: string) {
  const userRepository = getRepository(User);
  
  try {
    const user = await userRepository.findOne({
      where: { id: userId, isActive: true }
    });
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    return user;
  } catch (error) {
    console.error('获取用户资料失败:', error);
    throw error;
  }
}
```

3. **Mongoose** - MongoDB的ORM框架：

```javascript
// Mongoose示例
const mongoose = require('mongoose');

// 连接MongoDB
mongoose.connect('mongodb://localhost:27017/myapp', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// 定义Schema
const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    validate: {
      validator: function(v) {
        return /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(v);
      },
      message: '请提供有效的邮箱地址'
    }
  },
  password: {
    type: String,
    required: true,
    minlength: 8
  },
  isActive: {
    type: Boolean,
    default: true
  },
  roles: {
    type: [String],
    default: ['user']
  }
}, {
  timestamps: true
});

// 中间件
userSchema.pre('save', async function(next) {
  // 如果密码被修改则哈希处理
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, 10);
  }
  next();
});

// 实例方法
userSchema.methods.comparePassword = async function(password) {
  return bcrypt.compare(password, this.password);
};

// 静态方法
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email });
};

// 创建模型
const User = mongoose.model('User', userSchema);

// 使用模型
async function registerUser(userData) {
  try {
    const user = new User(userData);
    await user.save();
    return user;
  } catch (error) {
    if (error.code === 11000) { // 重复键错误
      throw new Error('用户名或邮箱已存在');
    }
    throw error;
  }
}

// 使用静态方法
async function authenticateUser(email, password) {
  try {
    // 使用自定义静态方法
    const user = await User.findByEmail(email);
    
    if (!user || !user.isActive) {
      throw new Error('用户不存在或已被禁用');
    }
    
    // 使用实例方法验证密码
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      throw new Error('密码不正确');
    }
    
    return user;
  } catch (error) {
    console.error('认证失败:', error);
    throw error;
  }
}
```

4. **Prisma** - 现代数据库工具包：

```javascript
// Prisma示例
// schema.prisma
// datasource db {
//   provider = "postgresql"
//   url      = env("DATABASE_URL")
// }
// 
// model User {
//   id        String   @id @default(uuid())
//   username  String   @unique
//   email     String   @unique
//   password  String
//   isActive  Boolean  @default(true)
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
//   posts     Post[]
// }
// 
// model Post {
//   id        String   @id @default(uuid())
//   title     String
//   content   String?
//   published Boolean  @default(false)
//   author    User     @relation(fields: [authorId], references: [id])
//   authorId  String
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// 事务示例
async function createUserAndPost(userData, postData) {
  try {
    // 使用事务确保原子操作
    const result = await prisma.$transaction(async (tx) => {
      // 创建用户
      const user = await tx.user.create({
        data: userData
      });
      
      // 创建文章并关联用户
      const post = await tx.post.create({
        data: {
          ...postData,
          author: {
            connect: { id: user.id }
          }
        }
      });
      
      return { user, post };
    });
    
    return result;
  } catch (error) {
    console.error('创建用户和文章失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 复杂查询示例
async function getUserWithPosts(userId) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        posts: {
          where: { published: true },
          orderBy: { createdAt: 'desc' }
        }
      }
    });
    
    return user;
  } catch (error) {
    console.error('获取用户和文章失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}
```

**3. 连接池管理**：

对于生产环境的数据库操作，使用连接池是提高性能和资源利用的关键。

```javascript
// MySQL连接池示例
const mysql = require('mysql2');

const pool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  password: 'password',
  database: 'myapp',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 获取Promise封装的池
const promisePool = pool.promise();

async function queryUsers() {
  try {
    // 从池中获取连接
    const [rows, fields] = await promisePool.query('SELECT * FROM users');
    return rows;
  } catch (error) {
    console.error('查询出错:', error);
    throw error;
  }
}
```

**4. 数据库迁移和版本控制**：

ORM框架通常提供迁移工具，用于数据库结构的版本控制：

```javascript
// Sequelize迁移示例
// migrations/20220601000000-create-users.js
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Users', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      username: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      password: {
        type: Sequelize.STRING,
        allowNull: false
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });
  },
  
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Users');
  }
};

// 运行迁移: npx sequelize-cli db:migrate
```

**5. 数据库设计最佳实践**：

无论使用哪种数据库解决方案，以下最佳实践适用于Node.js应用程序：

1. **使用环境变量存储数据库凭据**：
   ```javascript
   // 使用dotenv管理环境变量
   require('dotenv').config();
   
   const dbConfig = {
     host: process.env.DB_HOST,
     user: process.env.DB_USER,
     password: process.env.DB_PASSWORD,
     database: process.env.DB_NAME
   };
   ```

2. **实现仓储模式(Repository Pattern)**：
   ```javascript
   // 仓储模式示例
   class UserRepository {
     constructor(db) {
       this.db = db;
     }
     
     async findById(id) {
       return this.db.User.findByPk(id);
     }
     
     async findAll(options = {}) {
       return this.db.User.findAll(options);
     }
     
     async create(data) {
       return this.db.User.create(data);
     }
     
     async update(id, data) {
       const user = await this.findById(id);
       if (!user) throw new Error('User not found');
       return user.update(data);
     }
     
     async delete(id) {
       const user = await this.findById(id);
       if (!user) throw new Error('User not found');
       return user.destroy();
     }
   }
   
   // 使用仓储
   const userRepo = new UserRepository(db);
   const user = await userRepo.findById(1);
   ```

3. **事务管理**：
   ```javascript
   // Sequelize事务示例
   async function transferMoney(fromAccountId, toAccountId, amount) {
     // 开始事务
     const t = await sequelize.transaction();
     
     try {
       // 减少发送方账户余额
       const fromAccount = await Account.findByPk(fromAccountId, { 
         transaction: t, 
         lock: true 
       });
       
       if (fromAccount.balance < amount) {
         throw new Error('余额不足');
       }
       
       await fromAccount.decrement('balance', { by: amount, transaction: t });
       
       // 增加接收方账户余额
       const toAccount = await Account.findByPk(toAccountId, { 
         transaction: t,
         lock: true 
       });
       
       await toAccount.increment('balance', { by: amount, transaction: t });
       
       // 如果都成功，提交事务
       await t.commit();
       
       return { success: true };
     } catch (error) {
       // 如果出错，回滚事务
       await t.rollback();
       console.error('转账失败:', error);
       throw error;
     }
   }
   ```

4. **查询优化**：
   ```javascript
   // 优化查询示例 (Sequelize)
   async function getProductsWithCategory(page = 1, limit = 10) {
     const offset = (page - 1) * limit;
     
     // 1. 只选择需要的字段
     // 2. 使用包含条件的关联加载
     // 3. 实现分页
     const products = await Product.findAndCountAll({
       attributes: ['id', 'name', 'price', 'stock'], // 只选择需要的字段
       include: [
         {
           model: Category,
           attributes: ['id', 'name'], // 只选择需要的关联字段
           where: { isActive: true } // 条件关联加载
         }
       ],
       where: { 
         isPublished: true,
         price: { [Op.gte]: 10 } // 价格>=10
       },
       order: [['createdAt', 'DESC']],
       limit,
       offset
     });
     
     return {
       total: products.count,
       data: products.rows,
       page,
       totalPages: Math.ceil(products.count / limit)
     };
   }
   ```

5. **N+1查询问题解决**：
   ```javascript
   // 解决N+1问题 (Mongoose)
   
   // 不好的做法 - 会导致N+1查询
   async function getBlogPostsWithAuthor_Bad() {
     const posts = await Post.find();
     
     // 对每篇文章都要单独查询作者，导致N+1查询
     for (let post of posts) {
       post.author = await User.findById(post.authorId);
     }
     
     return posts;
   }
   
   // 好的做法 - 使用populate
   async function getBlogPostsWithAuthor_Good() {
     // 在单次查询中获取所有文章及其作者
     const posts = await Post.find().populate('author', 'name email');
     return posts;
   }
   ```

**6. NoSQL vs SQL在Node.js中的应用场景**：

| 数据库类型 | 适用场景 | Node.js解决方案 |
|-----------|---------|----------------|
| **关系型数据库** | 结构化数据、事务一致性、复杂查询 | Sequelize, TypeORM, Knex, Prisma |
| **文档型数据库** | 半结构化数据、快速迭代、水平扩展 | Mongoose, MongoDB Native Driver |
| **键值存储** | 缓存、会话存储、实时应用 | redis, ioredis, node-redis |
| **图数据库** | 关系网络、推荐系统、社交网络 | neo4j-driver, node-neo4j |

**7. 实际项目架构示例**：

```javascript
// 分层架构示例
// db/index.js - 数据库连接
const { Sequelize } = require('sequelize');
const config = require('../config/database');

const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    dialect: config.dialect,
    logging: config.logging
  }
);

module.exports = sequelize;

// models/user.js - 模型定义
const { DataTypes } = require('sequelize');
const sequelize = require('../db');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  // 其他字段...
});

module.exports = User;

// repositories/user-repository.js - 仓储层
class UserRepository {
  async findById(id) {
    return User.findByPk(id);
  }
  
  // 其他方法...
}

// services/user-service.js - 服务层
class UserService {
  constructor(userRepository) {
    this.userRepository = userRepository;
  }
  
  async getUserById(id) {
    const user = await this.userRepository.findById(id);
    if (!user) {
      throw new Error('User not found');
    }
    return this.sanitizeUser(user);
  }
  
  sanitizeUser(user) {
    // 移除敏感信息
    const { password, ...sanitizedUser } = user.get({ plain: true });
    return sanitizedUser;
  }
}

// controllers/user-controller.js - 控制器层
class UserController {
  constructor(userService) {
    this.userService = userService;
  }
  
  async getUser(req, res) {
    try {
      const user = await this.userService.getUserById(req.params.id);
      res.json(user);
    } catch (error) {
      res.status(404).json({ error: error.message });
    }
  }
}

// app.js - 依赖注入
const userRepository = new UserRepository();
const userService = new UserService(userRepository);
const userController = new UserController(userService);

app.get('/users/:id', (req, res) => userController.getUser(req, res));
```

**8. 数据库安全性考虑**：

1. **防止SQL注入**:
   ```javascript
   // 不安全的做法
   const userId = req.params.id;
   const query = `SELECT * FROM users WHERE id = ${userId}`; // 危险!
   
   // 安全的做法
   // 使用参数化查询
   const [rows] = await connection.execute(
     'SELECT * FROM users WHERE id = ?', 
     [userId]
   );
   ```

2. **密码安全**:
   ```javascript
   // 使用bcrypt哈希密码
   const bcrypt = require('bcrypt');
   
   async function hashPassword(password) {
     const saltRounds = 10;
     return bcrypt.hash(password, saltRounds);
   }
   
   async function verifyPassword(password, hash) {
     return bcrypt.compare(password, hash);
   }
   ```

3. **敏感数据保护**:
   ```javascript
   // 在ORM模型中排除敏感字段
   const User = sequelize.define('User', {
     // 字段定义
   }, {
     defaultScope: {
       attributes: { exclude: ['password', 'secret'] }
     },
     scopes: {
       withPassword: {
         attributes: { exclude: [] }
       }
     }
   });
   
   // 正常查询不返回密码
   const user = await User.findByPk(id);
   
   // 需要密码时使用特定scope
   const userWithPassword = await User.scope('withPassword').findByPk(id);
   ```

**考察重点**：
- 理解Node.js中不同ORM框架的特点和适用场景
- 掌握数据库连接池、事务管理和查询优化技术
- 能够设计合理的数据库访问模式和架构
- 了解数据库安全性最佳实践
- 根据业务需求选择合适的数据库解决方案

## 前端架构和微前端

### 1. 什么是微前端？它解决了哪些问题，有哪些实现方式？

**答案**：
微前端是一种前端架构风格，将前端应用分解成更小、更简单的应用单元，由独立团队开发和部署，同时集成到一个统一的产品中形成完整的用户体验。

**核心理念**：

1. **应用自治**：每个微前端应用可以独立开发、测试和部署
2. **技术栈无关**：各团队可以选择自己熟悉的技术栈
3. **团队自治**：每个团队负责自己的业务领域
4. **原生浏览器支持**：尽量使用浏览器原生特性而非自定义API
5. **渐进增强**：逐步从单体应用迁移到微前端架构

**微前端解决的问题**：

1. **大型前端应用的复杂性**：
   - 将庞大的代码库分解为更小、更易管理的部分
   - 减少不同功能区域之间的耦合

2. **团队协作的效率**：
   - 不同团队可以独立工作，减少协调成本
   - 避免代码冲突和合并问题

3. **技术栈的演进**：
   - 允许在同一应用中使用不同技术栈
   - 渐进式升级或替换技术栈，而不必完全重写

4. **持续部署**：
   - 独立部署各个微前端应用
   - 减少发布风险，提高发布频率

5. **遗留系统整合**：
   - 逐步将旧系统迁移到新架构
   - 新旧系统共存，平滑过渡

**主要实现方式**：

1. **基于路由的分发集成**：

```javascript
// 主应用路由配置
const routes = [
  {
    path: '/app1',
    component: () => import('@org/app1') // 动态加载微应用1
  },
  {
    path: '/app2',
    component: () => import('@org/app2') // 动态加载微应用2
  }
];
```

**优点**：简单直接，每个路由对应一个微前端应用
**缺点**：只能在页面级别集成，无法在同一页面组合多个微前端

2. **使用iframes**：

```html
<!-- 主应用 -->
<div>
  <header>主应用导航</header>
  <iframe id="micro-frontend-container" src="https://app1.example.com"></iframe>
</div>

<script>
  // 通过消息传递与iframe通信
  window.addEventListener('message', event => {
    if (event.origin === 'https://app1.example.com') {
      // 处理来自微前端的消息
      console.log(event.data);
    }
  });
  
  // 向微前端发送消息
  document.getElementById('micro-frontend-container').contentWindow
    .postMessage({ type: 'INIT', payload: {...} }, 'https://app1.example.com');
</script>
```

**优点**：最强的隔离性，技术栈完全独立
**缺点**：样式和DOM隔离导致集成度低，用户体验不连贯

3. **Web Components**：

```javascript
// app1/index.js - 微前端应用1
class MicroApp1 extends HTMLElement {
  connectedCallback() {
    this.innerHTML = `<div>
      <h2>微前端应用1</h2>
      <button id="app1-button">点击我</button>
    </div>`;
    
    this.querySelector('#app1-button').addEventListener('click', () => {
      // 处理点击事件
    });
  }
}

// 注册自定义元素
customElements.define('micro-app-1', MicroApp1);
```

```html
<!-- 主应用 -->
<div>
  <header>主应用导航</header>
  <micro-app-1></micro-app-1>
  <micro-app-2></micro-app-2>
</div>
```

**优点**：浏览器原生支持，好的封装性
**缺点**：不同组件间的通信需要额外处理，老浏览器兼容性问题

4. **JavaScript集成（运行时集成）**：

```javascript
// 主应用
import { mountApp1 } from '@org/app1';
import { mountApp2 } from '@org/app2';

// 挂载微前端
mountApp1(document.getElementById('app1-container'));
mountApp2(document.getElementById('app2-container'));
```

```javascript
// app1/index.js
export function mountApp1(container) {
  // 可以是任何框架，如React, Vue, Angular等
  ReactDOM.render(<App1 />, container);
  
  return {
    unmount: () => {
      ReactDOM.unmountComponentAtNode(container);
    }
  };
}
```

**优点**：灵活性高，可以在页面任何位置集成微前端
**缺点**：需要约定统一的接口规范，可能存在依赖冲突

5. **基于Module Federation的集成(Webpack 5)**：

```javascript
// 微前端应用1的webpack配置
module.exports = {
  // ...
  plugins: [
    new ModuleFederationPlugin({
      name: 'app1',
      filename: 'remoteEntry.js',
      exposes: {
        './Button': './src/components/Button',
        './Header': './src/components/Header'
      },
      shared: ['react', 'react-dom']
    })
  ]
};

// 主应用的webpack配置
module.exports = {
  // ...
  plugins: [
    new ModuleFederationPlugin({
      name: 'host',
      remotes: {
        app1: 'app1@http://localhost:3001/remoteEntry.js',
        app2: 'app2@http://localhost:3002/remoteEntry.js'
      },
      shared: ['react', 'react-dom']
    })
  ]
};
```

```javascript
// 主应用中使用微前端组件
import React, { lazy, Suspense } from 'react';

// 动态导入来自微前端的组件
const RemoteButton = lazy(() => import('app1/Button'));
const RemoteHeader = lazy(() => import('app1/Header'));

function App() {
  return (
    <div>
      <Suspense fallback="加载中...">
        <RemoteHeader />
        <RemoteButton />
      </Suspense>
    </div>
  );
}
```

**优点**：共享依赖减少重复加载，组件级别集成
**缺点**：配置复杂，要求使用Webpack 5

6. **使用微前端框架（如Single-SPA, qiankun）**：

```javascript
// 使用qiankun注册微应用
import { registerMicroApps, start } from 'qiankun';

registerMicroApps([
  {
    name: 'app1',
    entry: '//localhost:3001',
    container: '#app1-container',
    activeRule: '/app1',
    props: { shared: sharedData }
  },
  {
    name: 'app2',
    entry: '//localhost:3002',
    container: '#app2-container',
    activeRule: '/app2',
    props: { shared: sharedData }
  }
]);

// 启动微前端应用
start();
```

**优点**：成熟的解决方案，处理了样式隔离、生命周期管理等问题
**缺点**：增加了学习成本，可能有一定性能开销

**微前端架构的核心挑战**：

1. **样式隔离**：
   - CSS-in-JS
   - Shadow DOM
   - CSS命名空间（如BEM）
   - 动态样式隔离（如qiankun的沙箱机制）

2. **JavaScript隔离**：
   - 使用闭包和IIFE
   - Web Worker
   - 沙箱机制（如qiankun, single-spa）
   - iframe自然隔离

3. **应用间通信**：
   - 基于URL和路由参数
   - 自定义事件
   - 共享状态库（如Redux）
   - 通过Props传递
   - 发布-订阅模式

4. **统一用户体验**：
   - 共享设计系统和组件库
   - 统一认证和授权
   - 一致的导航和布局

5. **性能优化**：
   - 共享公共依赖
   - 懒加载微应用
   - 预加载策略
   - 缓存机制

**微前端架构选型考虑因素**：

1. **项目规模和团队结构**
2. **现有技术栈和遗留系统情况**
3. **对用户体验的要求**
4. **对开发效率的要求**
5. **部署环境和基础设施**

**微前端的典型架构示例**：

```
┌─────────────────────────────────────────────────────────┐
│                     Shell Application                    │
│                                                          │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────┐    │
│  │             │   │             │   │             │    │
│  │  Micro      │   │  Micro      │   │  Micro      │    │
│  │  Frontend 1 │   │  Frontend 2 │   │  Frontend 3 │    │
│  │  (React)    │   │  (Vue)      │   │  (Angular)  │    │
│  │             │   │             │   │             │    │
│  └─────────────┘   └─────────────┘   └─────────────┘    │
│                                                          │
│  ┌─────────────────────────────────────────────────┐    │
│  │              Shared Components                   │    │
│  └─────────────────────────────────────────────────┘    │
│                                                          │
└─────────────────────────────────────────────────────────┘
```

**考察重点**：
- 理解微前端的核心理念和解决的问题
- 熟悉不同微前端实现方式的优缺点
- 掌握微前端架构中的关键技术挑战及解决方案
- 能够根据项目需求选择合适的微前端架构方案
- 了解主流微前端框架（如Single-SPA、qiankun）的使用方式

### 2. 前端架构设计的核心原则和常见模式有哪些？

**答案**：
前端架构设计是构建可扩展、可维护和高性能前端应用的系统性方法，它涉及组织代码、选择技术栈、定义开发规范和优化用户体验等多个方面。

**前端架构设计的核心原则**：

1. **关注点分离(SoC)**：
   - 将应用划分为不同层次和模块，每个部分专注于特定功能
   - 例如：将UI、业务逻辑、数据处理分离

2. **单一职责原则(SRP)**：
   - 每个组件或模块应只有一个变更的理由
   - 有助于创建更加内聚、更易测试的代码

3. **最小知识原则(LoD)**：
   - 组件之间的交互应该最小化，减少依赖
   - "迪米特法则"：只与直接的朋友通信

4. **抽象与封装**：
   - 隐藏实现细节，提供清晰的接口
   - 降低系统复杂度，提高代码可读性

5. **可测试性**：
   - 设计应便于自动化测试
   - 支持单元测试、集成测试和端到端测试

6. **渐进式增强**：
   - 确保核心功能在各种环境下可用
   - 根据浏览器能力逐步增强用户体验

7. **性能优先**：
   - 在设计阶段就考虑性能问题
   - 优化首屏加载时间、交互响应时间等关键指标

8. **可扩展性**：
   - 架构应能轻松适应功能扩展和规模增长
   - 避免过度设计，但预留扩展空间

**前端常见架构模式**：

1. **MVC (Model-View-Controller)**：

```javascript
// Model - 数据和业务逻辑
class UserModel {
  constructor() {
    this.users = [];
  }
  
  addUser(user) {
    this.users.push(user);
  }
  
  getUsers() {
    return this.users;
  }
}

// View - 用户界面
class UserView {
  constructor() {
    this.userList = document.getElementById('user-list');
    this.addButton = document.getElementById('add-user');
    this.nameInput = document.getElementById('user-name');
  }
  
  render(users) {
    this.userList.innerHTML = '';
    users.forEach(user => {
      const li = document.createElement('li');
      li.textContent = user.name;
      this.userList.appendChild(li);
    });
  }
  
  bindAddUser(handler) {
    this.addButton.addEventListener('click', () => {
      handler(this.nameInput.value);
      this.nameInput.value = '';
    });
  }
}

// Controller - 协调Model和View
class UserController {
  constructor(model, view) {
    this.model = model;
    this.view = view;
    
    this.view.bindAddUser(this.handleAddUser.bind(this));
    this.updateView();
  }
  
  handleAddUser(name) {
    if (name.trim()) {
      this.model.addUser({ name });
      this.updateView();
    }
  }
  
  updateView() {
    this.view.render(this.model.getUsers());
  }
}

// 初始化应用
const app = new UserController(new UserModel(), new UserView());
```

**特点**：
- 清晰的责任划分
- 易于理解和实现
- 在简单应用中效果好
- 复杂应用中可能导致Controller膨胀

2. **MVP (Model-View-Presenter)**：

```javascript
// MVP示例
// Model - 数据层
class UserModel {
  constructor() {
    this.users = [];
  }
  
  addUser(user) {
    return new Promise((resolve) => {
      // 模拟API调用
      setTimeout(() => {
        this.users.push(user);
        resolve(this.users);
      }, 100);
    });
  }
  
  getUsers() {
    return Promise.resolve([...this.users]);
  }
}

// View - 界面层(被动)
class UserView {
  constructor() {
    this.userList = document.getElementById('user-list');
    this.addButton = document.getElementById('add-user');
    this.nameInput = document.getElementById('user-name');
  }
  
  bindAddUser(handler) {
    this.addButton.addEventListener('click', () => {
      const name = this.nameInput.value.trim();
      if (name) {
        handler(name);
        this.nameInput.value = '';
      }
    });
  }
  
  displayUsers(users) {
    this.userList.innerHTML = '';
    users.forEach(user => {
      const li = document.createElement('li');
      li.textContent = user.name;
      this.userList.appendChild(li);
    });
  }
  
  showLoading() {
    // 显示加载状态
  }
  
  hideLoading() {
    // 隐藏加载状态
  }
}

// Presenter - 协调层
class UserPresenter {
  constructor(model, view) {
    this.model = model;
    this.view = view;
    
    this.view.bindAddUser(this.handleAddUser.bind(this));
    this.loadUsers();
  }
  
  async handleAddUser(name) {
    this.view.showLoading();
    try {
      await this.model.addUser({ name });
      await this.loadUsers();
    } finally {
      this.view.hideLoading();
    }
  }
  
  async loadUsers() {
    this.view.showLoading();
    try {
      const users = await this.model.getUsers();
      this.view.displayUsers(users);
    } finally {
      this.view.hideLoading();
    }
  }
}

// 初始化
const userPresenter = new UserPresenter(new UserModel(), new UserView());
```

**特点**：
- View完全被动，不包含状态逻辑
- Presenter直接操作View，双向通信
- 便于单元测试
- 比MVC更好地分离关注点

3. **MVVM (Model-View-ViewModel)**：

```javascript
// MVVM示例 (使用Vue.js)
// Model - 数据模型
const userModel = {
  fetchUsers() {
    return fetch('/api/users').then(r => r.json());
  },
  
  createUser(user) {
    return fetch('/api/users', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(user)
    }).then(r => r.json());
  }
};

// ViewModel - 视图模型
const UserViewModel = {
  data() {
    return {
      users: [],
      newUser: { name: '' },
      isLoading: false
    };
  },
  
  created() {
    this.loadUsers();
  },
  
  methods: {
    async loadUsers() {
      this.isLoading = true;
      try {
        this.users = await userModel.fetchUsers();
      } finally {
        this.isLoading = false;
      }
    },
    
    async addUser() {
      if (!this.newUser.name.trim()) return;
      
      this.isLoading = true;
      try {
        await userModel.createUser(this.newUser);
        this.newUser = { name: '' };
        await this.loadUsers();
      } finally {
        this.isLoading = false;
      }
    }
  }
};

// View - 视图(通过声明式绑定)
/* HTML
<div id="app">
  <div v-if="isLoading">加载中...</div>
  <ul>
    <li v-for="user in users" :key="user.id">{{ user.name }}</li>
  </ul>
  <input v-model="newUser.name" placeholder="输入用户名" />
  <button @click="addUser" :disabled="isLoading">添加用户</button>
</div>
*/

// 初始化
const app = Vue.createApp(UserViewModel).mount('#app');
```

**特点**：
- 视图和ViewModel通过数据绑定自动同步
- 声明式编程，减少DOM操作
- 更好的关注点分离
- 现代前端框架(Vue, Angular)常用的模式

4. **Flux/Redux架构**：

```javascript
// Redux架构示例
// 1. Actions
const ADD_USER = 'ADD_USER';
const FETCH_USERS = 'FETCH_USERS';

const addUser = (user) => ({
  type: ADD_USER,
  payload: user
});

const fetchUsers = () => {
  return async (dispatch) => {
    dispatch({ type: FETCH_USERS, status: 'loading' });
    
    try {
      const response = await fetch('/api/users');
      const users = await response.json();
      dispatch({ type: FETCH_USERS, status: 'success', payload: users });
    } catch (error) {
      dispatch({ type: FETCH_USERS, status: 'error', error });
    }
  };
};

// 2. Reducer
const initialState = {
  users: [],
  loading: false,
  error: null
};

function userReducer(state = initialState, action) {
  switch (action.type) {
    case ADD_USER:
      return {
        ...state,
        users: [...state.users, action.payload]
      };
      
    case FETCH_USERS:
      if (action.status === 'loading') {
        return {
          ...state,
          loading: true,
          error: null
        };
      } else if (action.status === 'success') {
        return {
          ...state,
          users: action.payload,
          loading: false
        };
      } else if (action.status === 'error') {
        return {
          ...state,
          loading: false,
          error: action.error
        };
      }
      return state;
      
    default:
      return state;
  }
}

// 3. Store
const store = Redux.createStore(
  userReducer,
  Redux.applyMiddleware(ReduxThunk)
);

// 4. React组件(View)
function UserList({ users, loading, error, onAddUser, onFetchUsers }) {
  const [name, setName] = React.useState('');
  
  React.useEffect(() => {
    onFetchUsers();
  }, [onFetchUsers]);
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (name.trim()) {
      onAddUser({ name });
      setName('');
    }
  };
  
  if (loading) return <div>加载中...</div>;
  if (error) return <div>加载出错: {error.message}</div>;
  
  return (
    <div>
      <ul>
        {users.map(user => (
          <li key={user.id}>{user.name}</li>
        ))}
      </ul>
      
      <form onSubmit={handleSubmit}>
        <input 
          value={name}
          onChange={e => setName(e.target.value)}
          placeholder="输入用户名"
        />
        <button type="submit">添加用户</button>
      </form>
    </div>
  );
}

// 5. 连接Redux和React
const ConnectedUserList = ReactRedux.connect(
  // mapStateToProps
  state => ({
    users: state.users,
    loading: state.loading,
    error: state.error
  }),
  // mapDispatchToProps
  dispatch => ({
    onAddUser: user => dispatch(addUser(user)),
    onFetchUsers: () => dispatch(fetchUsers())
  })
)(UserList);

// 6. 渲染应用
ReactDOM.render(
  <ReactRedux.Provider store={store}>
    <ConnectedUserList />
  </ReactRedux.Provider>,
  document.getElementById('root')
);
```

**特点**：
- 单向数据流
- 可预测的状态管理
- 便于调试和测试
- 适合复杂应用
- 可能有较多的样板代码

5. **组件化架构**：

```javascript
// React组件化架构示例
// 原子组件
const Button = ({ children, onClick, disabled }) => (
  <button 
    className="btn" 
    onClick={onClick} 
    disabled={disabled}
  >
    {children}
  </button>
);

const Input = ({ value, onChange, placeholder }) => (
  <input
    className="input"
    value={value}
    onChange={e => onChange(e.target.value)}
    placeholder={placeholder}
  />
);

// 分子组件
const UserForm = ({ onSubmit }) => {
  const [name, setName] = React.useState('');
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (name.trim()) {
      onSubmit(name);
      setName('');
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="user-form">
      <Input 
        value={name}
        onChange={setName}
        placeholder="输入用户名"
      />
      <Button type="submit">添加</Button>
    </form>
  );
};

const UserList = ({ users }) => (
  <ul className="user-list">
    {users.map(user => (
      <li key={user.id} className="user-item">{user.name}</li>
    ))}
  </ul>
);

// 组织组件
const UserManager = () => {
  const [users, setUsers] = React.useState([]);
  
  const addUser = (name) => {
    // 在实际应用中，这里会调用API
    const newUser = { id: Date.now(), name };
    setUsers([...users, newUser]);
  };
  
  return (
    <div className="user-manager">
      <h1>用户管理</h1>
      <UserForm onSubmit={addUser} />
      <UserList users={users} />
    </div>
  );
};

// 页面组件
const App = () => (
  <div className="app">
    <header className="app-header">
      <h1>我的应用</h1>
    </header>
    <main className="app-main">
      <UserManager />
    </main>
    <footer className="app-footer">
      <p>© 2023 示例应用</p>
    </footer>
  </div>
);

// 渲染应用
ReactDOM.render(<App />, document.getElementById('root'));
```

**特点**：
- 组合优于继承
- 可重用的UI组件
- 遵循原子设计方法论
- 适合设计系统的实现

6. **微服务/微前端架构**：
   (见前一个问题的详细解答)

7. **服务端渲染(SSR)架构**：

```javascript
// Next.js SSR架构示例
// pages/users.js
import { useState } from 'react';
import { useRouter } from 'next/router';

// 服务端获取数据
export async function getServerSideProps() {
  try {
    const res = await fetch('https://api.example.com/users');
    const users = await res.json();
    return { props: { initialUsers: users } };
  } catch (error) {
    return { props: { initialUsers: [], error: error.message } };
  }
}

// 服务端渲染的组件
export default function UsersPage({ initialUsers, error }) {
  const router = useRouter();
  const [users, setUsers] = useState(initialUsers);
  const [newUser, setNewUser] = useState('');
  
  const handleAddUser = async (e) => {
    e.preventDefault();
    if (!newUser.trim()) return;
    
    try {
      const res = await fetch('https://api.example.com/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newUser })
      });
      
      const addedUser = await res.json();
      setUsers([...users, addedUser]);
      setNewUser('');
    } catch (error) {
      console.error('添加用户失败:', error);
    }
  };
  
  if (error) {
    return <div>加载出错: {error}</div>;
  }
  
  return (
    <div>
      <h1>用户列表</h1>
      
      <ul>
        {users.map(user => (
          <li key={user.id}>{user.name}</li>
        ))}
      </ul>
      
      <form onSubmit={handleAddUser}>
        <input
          value={newUser}
          onChange={e => setNewUser(e.target.value)}
          placeholder="输入用户名"
        />
        <button type="submit">添加用户</button>
      </form>
    </div>
  );
}
```

**特点**：
- 改善首屏加载速度和SEO
- 服务端和客户端混合渲染
- 增加服务器负载
- 现代框架(Next.js, Nuxt.js)简化实现

8. **JAMStack架构**：

```javascript
// Gatsby.js JAMStack示例
// gatsby-node.js
exports.createPages = async ({ graphql, actions }) => {
  const { createPage } = actions;
  
  // 查询Markdown文件
  const result = await graphql(`
    query {
      allMarkdownRemark {
        edges {
          node {
            frontmatter {
              slug
            }
          }
        }
      }
    }
  `);
  
  // 为每篇文章创建页面
  result.data.allMarkdownRemark.edges.forEach(({ node }) => {
    createPage({
      path: node.frontmatter.slug,
      component: require.resolve('./src/templates/article.js'),
      context: {
        slug: node.frontmatter.slug
      }
    });
  });
};

// src/templates/article.js
import React from 'react';
import { graphql } from 'gatsby';

export const query = graphql`
  query($slug: String!) {
    markdownRemark(frontmatter: { slug: { eq: $slug } }) {
      html
      frontmatter {
        title
        date
      }
    }
  }
`;

const ArticleTemplate = ({ data }) => {
  const { markdownRemark } = data;
  const { frontmatter, html } = markdownRemark;
  
  return (
    <article>
      <h1>{frontmatter.title}</h1>
      <p>发布日期: {frontmatter.date}</p>
      <div dangerouslySetInnerHTML={{ __html: html }} />
    </article>
  );
};

export default ArticleTemplate;
```

**特点**：
- 预构建静态资源
- 无服务器端运行时
- 通过API集成服务
- 极佳的性能和安全性

**选择架构模式的考虑因素**：

1. **应用复杂度**：
   - 简单应用可选择MVC或组件化架构
   - 复杂应用需要考虑Flux/Redux或微前端

2. **团队规模和结构**：
   - 大团队更需要关注分治和独立开发
   - 团队技能水平影响架构选择

3. **性能需求**：
   - 首屏加载要求高可选择SSR或JAMStack
   - 交互密集型应用需优化运行时性能

4. **扩展性要求**：
   - 考虑未来功能扩展和规模增长
   - 预留接口和扩展点

5. **兼容性需求**：
   - 浏览器兼容性范围
   - 是否需要支持降级方案

**实际项目中的架构组合**：

实际项目中往往采用多种架构模式的组合：

```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│  ┌─────────────┐          ┌──────────────────────────┐  │
│  │             │          │                          │  │
│  │  静态内容    │          │  动态内容 (SPA)          │  │
│  │  JAMStack   │          │  Redux + 组件化架构       │  │
│  │             │          │                          │  │
│  └─────────────┘          └──────────────────────────┘  │
│                                                         │
│  ┌─────────────────────────────────────────────────┐    │
│  │              服务端渲染的关键页面                 │    │
│  │              (SSR架构, 如Next.js)               │    │
│  └─────────────────────────────────────────────────┘    │
│                                                         │
│  ┌─────────────────────────────────────────────────┐    │
│  │              共享组件库和设计系统                 │    │
│  └─────────────────────────────────────────────────┘    │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

**考察重点**：
- 理解不同前端架构模式的特点、优势和适用场景
- 能够根据具体需求选择合适的架构模式
- 掌握前端架构设计的核心原则
- 了解如何组合不同架构模式解决复杂问题
- 能够评估架构决策对性能、可维护性和开发效率的影响