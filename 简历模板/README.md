# 简历生成系统

这个项目提供了一套用于生成不同格式简历的工具和模板。

## 项目结构

```
简历模板/
├── Python脚本/        - 用于生成简历的Python脚本
│   └── resume_env/   - Python虚拟环境
├── 中文简历/          - 中文简历文件和源文件
└── 英文简历/          - 英文简历文件和源文件
```

## Python脚本说明

- `create_chinese_resume.py` - 从TXT文件生成中文简历Word文档，使用苹方字体
- `create_resume.py` - 生成英文格式的简历Word文档
- `create_optimized_resume.py` - 生成优化格式的英文简历Word文档，布局和样式更加精美
- `run_scripts.sh` - 命令行菜单工具，可以方便地选择生成不同类型的简历

## 使用方法

1. 确保已安装 Python 和必要的库（python-docx）
2. 使用 resume_env 虚拟环境执行脚本：

```bash
# 使用交互式菜单（推荐）
./简历模板/Python脚本/run_scripts.sh

# 或直接执行特定脚本
./简历模板/Python脚本/resume_env/bin/python 简历模板/Python脚本/create_chinese_resume.py  # 生成中文简历
./简历模板/Python脚本/resume_env/bin/python 简历模板/Python脚本/create_resume.py          # 生成英文简历
./简历模板/Python脚本/resume_env/bin/python 简历模板/Python脚本/create_optimized_resume.py # 生成优化版英文简历
```

## 简历文件

### 中文简历

- `六年前端-谢荣飞.txt` - 原始文本格式简历
- `谢荣飞-前端开发简历-简洁版.docx` - 使用苹方字体的简洁版Word简历
- `六年前端-谢荣飞.pdf/.docx` - 其他格式的中文简历

### 英文简历

- `Frontend-Resume-XieRongfei-EN.txt` - 原始文本格式英文简历
- `XIE_RONGFEI_Frontend_Resume.docx/.pdf` - 标准英文简历
- `XIE_RONGFEI_Optimized_Frontend_Resume.docx` - 优化版英文简历 