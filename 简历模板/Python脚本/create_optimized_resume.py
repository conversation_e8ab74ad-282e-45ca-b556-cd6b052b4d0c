import os
from docx import Document
from docx.shared import Pt, Inches, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.ns import qn

# 创建文档对象
doc = Document()

# 设置页面边距
sections = doc.sections
for section in sections:
    section.top_margin = Inches(0.7)
    section.bottom_margin = Inches(0.7)
    section.left_margin = Inches(0.7)
    section.right_margin = Inches(0.7)

# 创建样式
styles = doc.styles

# 名称样式
name_style = styles.add_style('Name Style', WD_STYLE_TYPE.PARAGRAPH)
name_font = name_style.font
name_font.name = 'Arial'
name_font.size = Pt(20)
name_font.bold = True
name_font.color.rgb = RGBColor(31, 73, 125)  # 职业蓝色

# 标题样式 - 主要部分标题
title_style = styles.add_style('Title Style', WD_STYLE_TYPE.PARAGRAPH)
title_font = title_style.font
title_font.name = 'Arial'
title_font.size = Pt(14)
title_font.bold = True
title_font.color.rgb = RGBColor(31, 73, 125)  # 职业蓝色
title_paragraph = title_style.paragraph_format
title_paragraph.space_before = Pt(12)
title_paragraph.space_after = Pt(6)
title_paragraph.keep_with_next = True

# 子标题样式 - 公司/项目名称
subtitle_style = styles.add_style('Subtitle Style', WD_STYLE_TYPE.PARAGRAPH)
subtitle_font = subtitle_style.font
subtitle_font.name = 'Arial'
subtitle_font.size = Pt(12)
subtitle_font.bold = True
subtitle_paragraph = subtitle_style.paragraph_format
subtitle_paragraph.space_before = Pt(10)
subtitle_paragraph.space_after = Pt(2)
subtitle_paragraph.keep_with_next = True

# 职位样式 - 职位名称
position_style = styles.add_style('Position Style', WD_STYLE_TYPE.PARAGRAPH)
position_font = position_style.font
position_font.name = 'Arial'
position_font.size = Pt(11)
position_font.bold = True
position_font.italic = True
position_paragraph = position_style.paragraph_format
position_paragraph.space_after = Pt(2)
position_paragraph.keep_with_next = True

# 正文样式
normal_style = styles.add_style('Normal Style', WD_STYLE_TYPE.PARAGRAPH)
normal_font = normal_style.font
normal_font.name = 'Arial'
normal_font.size = Pt(11)
normal_paragraph = normal_style.paragraph_format
normal_paragraph.space_before = Pt(2)
normal_paragraph.space_after = Pt(2)

# 项目符号样式
bullet_style = styles.add_style('Bullet Style', WD_STYLE_TYPE.PARAGRAPH)
bullet_font = bullet_style.font
bullet_font.name = 'Arial'
bullet_font.size = Pt(11)
bullet_paragraph = bullet_style.paragraph_format
bullet_paragraph.space_before = Pt(1)
bullet_paragraph.space_after = Pt(1)
bullet_paragraph.left_indent = Inches(0.25)

# 分隔线样式
separator_style = styles.add_style('Separator Style', WD_STYLE_TYPE.PARAGRAPH)
separator_font = separator_style.font
separator_font.color.rgb = RGBColor(31, 73, 125)  # 职业蓝色
separator_font.size = Pt(6)
separator_paragraph = separator_style.paragraph_format
separator_paragraph.space_before = Pt(3)
separator_paragraph.space_after = Pt(6)

# 添加姓名和个人信息
name = doc.add_paragraph('XIE RONGFEI', style='Name Style')
name.alignment = WD_ALIGN_PARAGRAPH.CENTER

info = doc.add_paragraph('Frontend Engineer | Shanghai, China', style='Normal Style')
info.alignment = WD_ALIGN_PARAGRAPH.CENTER

contact = doc.add_paragraph('📱 +86 17607006309 | ✉️ <EMAIL> | 💼 linkedin.com/in/xierongfei', style='Normal Style')
contact.alignment = WD_ALIGN_PARAGRAPH.CENTER

# 添加分隔线
separator = doc.add_paragraph('_' * 120, style='Separator Style')
separator.alignment = WD_ALIGN_PARAGRAPH.CENTER

# 添加专业摘要
doc.add_paragraph('PROFESSIONAL SUMMARY', style='Title Style')
summary = doc.add_paragraph(style='Normal Style')
summary.add_run('Frontend Engineering Expert with 6 years of experience developing scalable, high-performance web applications. Specialized in ').bold = False
summary.add_run('Vue.js, React, and TypeScript').bold = True
summary.add_run(' with expertise in performance optimization and component architecture. Proven track record of reducing load times by ').bold = False
summary.add_run('30-65%').bold = True
summary.add_run(' and implementing solutions that improve user experiences. Skilled in leading technical teams and collaborating cross-functionally to deliver complex projects.').bold = False

# 添加核心能力
doc.add_paragraph('CORE COMPETENCIES', style='Title Style')

# 创建3列的表格来展示核心能力
skills_table = doc.add_table(rows=3, cols=3)
skills_table.style = 'Table Grid'
skills_table.autofit = True

# 表格中填入核心能力，以3列排列
skills = [
    '▪ Frontend Architecture', '▪ Performance Optimization', '▪ Vue.js & React Ecosystems',
    '▪ Component Development', '▪ Cross-platform Solutions', '▪ TypeScript/JavaScript',
    '▪ CI/CD Implementation', '▪ Team Leadership', '▪ Technical Problem Solving'
]

for i in range(3):
    for j in range(3):
        index = i * 3 + j
        cell = skills_table.cell(i, j)
        cell.text = skills[index]
        cell_paragraph = cell.paragraphs[0]
        cell_paragraph.style = 'Normal Style'

# 添加专业技能
doc.add_paragraph('TECHNICAL SKILLS', style='Title Style')

tech_skills = {
    'Frontend': 'JavaScript(ES6+), TypeScript, HTML5, CSS3/SCSS/SASS, Vue.js, React, Vuex, Redux',
    'UI Frameworks': 'Element UI, Ant Design, UView, Bootstrap, Tailwind CSS',
    'Build Tools': 'Webpack, Vite, Gulp, npm/yarn/pnpm, Babel',
    'Testing': 'Jest, Cypress, Vue Test Utils',
    'DevOps': 'Git, Docker, CI/CD, Linux Commands',
    'Backend Exposure': 'Node.js, Express, RESTful APIs, GraphQL',
    'Performance': 'Lighthouse, Chrome DevTools, Bundle Analysis, Code Splitting'
}

for category, skills in tech_skills.items():
    p = doc.add_paragraph(style='Normal Style')
    p.add_run(f'{category}: ').bold = True
    p.add_run(skills)

# 添加工作经验
doc.add_paragraph('PROFESSIONAL EXPERIENCE', style='Title Style')

# 第一份工作
company1 = doc.add_paragraph('SHANGHAI AIJUESI INFORMATION TECHNOLOGY CO., LTD.', style='Subtitle Style')
position1 = doc.add_paragraph('Senior Frontend Engineer / Frontend Development Lead', style='Position Style')
duration1 = doc.add_paragraph('December 2020 - February 2025', style='Normal Style')

job1_achievements = [
    'Led frontend architecture for 5+ enterprise applications, reducing code duplication by 40% through component reusability and increasing development efficiency by 30%',
    'Implemented multi-level permission control system using custom Vue directives, reducing authorization-related bugs by 75% and improving security across all applications',
    'Developed and published 20+ customizable components as npm packages (500+ downloads), streamlining development across multiple projects',
    'Spearheaded performance optimization initiatives that reduced initial load times by 30% and resolved memory leaks, improving overall application stability',
    'Mentored 8 junior developers through knowledge sharing sessions, code reviews, and pair programming, raising team capability and coding standards'
]

for ach in job1_achievements:
    p = doc.add_paragraph(style='Bullet Style')
    p.add_run(f'• {ach}')

# 第二份工作
company2 = doc.add_paragraph('HUCHENG INFORMATION TECHNOLOGY (SHANGHAI) CO., LTD.', style='Subtitle Style')
position2 = doc.add_paragraph('Intermediate Frontend Engineer', style='Position Style')
duration2 = doc.add_paragraph('July 2018 - December 2020', style='Normal Style')

job2_achievements = [
    'Implemented virtual list rendering for large datasets (10,000+ records), improving page rendering performance by 50% and reducing memory usage by 60%',
    'Optimized Echarts data visualization components, reducing complex chart loading time from 2000ms to 65ms (97% improvement)',
    'Established Git workflow standards and implemented Husky + ESLint + Prettier for automated code quality enforcement, reducing pull request revisions by 40%',
    'Contributed to component library development, creating reusable modules that decreased development time for new features by 25%'
]

for ach in job2_achievements:
    p = doc.add_paragraph(style='Bullet Style')
    p.add_run(f'• {ach}')

# 添加重点项目
doc.add_paragraph('KEY PROJECTS', style='Title Style')

# 项目1
project1 = doc.add_paragraph('POC Project Management Platform', style='Subtitle Style')
tech1 = doc.add_paragraph(style='Normal Style')
tech1.add_run('Technology: ').bold = True
tech1.add_run('Vue.js, Element UI, Vite, Axios')

p1_achievements = [
    'Architected dynamic approval workflow system supporting complex business rules and multi-role permissions, reducing process management overhead by 70%',
    'Implemented caching strategy for dashboard data, reducing API calls by 85% and improving responsiveness',
    'Created algorithm functions for node-based workflow processing, enabling 60% faster iterations during approval flow changes',
    'Built solution supporting 250+ daily active users with 99.8% uptime and no major bugs in production'
]

for ach in p1_achievements:
    p = doc.add_paragraph(style='Bullet Style')
    p.add_run(f'• {ach}')

# 项目2
project2 = doc.add_paragraph('Shanghai City Regulations Compendium (Multiplatform)', style='Subtitle Style')
tech2 = doc.add_paragraph(style='Normal Style')
tech2.add_run('Technology: ').bold = True
tech2.add_run('Vue.js, Webpack, Axios, Canvas API')

p2_achievements = [
    'Delivered responsive application supporting both desktop and mobile users with consistent UI/UX, increasing citizen access by 145%',
    'Engineered canvas-based image compression system reducing QR code sizes by 65%, significantly improving upload speeds',
    'Developed advanced search functionality with filtering capabilities processing 100,000+ legal records with sub-second response time',
    'Implemented code splitting and lazy loading, reducing bundle size by 40% and improving initial page load metrics'
]

for ach in p2_achievements:
    p = doc.add_paragraph(style='Bullet Style')
    p.add_run(f'• {ach}')

# 添加教育背景
doc.add_paragraph('EDUCATION', style='Title Style')
edu = doc.add_paragraph(style='Normal Style')
edu.add_run('Jiangxi Polytechnic College').bold = True
edu.add_run(' | Electronic Information Engineering | September 2015 - June 2018')
award = doc.add_paragraph(style='Bullet Style')
award.add_run('• Recipient of Second Prize in College-wide C Programming Competition')

# 添加其他信息
doc.add_paragraph('ADDITIONAL INFORMATION', style='Title Style')
languages = doc.add_paragraph(style='Normal Style')
languages.add_run('Languages: ').bold = True
languages.add_run('Mandarin Chinese (Native), English (Professional Working Proficiency)')

interests = doc.add_paragraph(style='Normal Style')
interests.add_run('Continuous Learning: ').bold = True
interests.add_run('Dedicates 1.5 hours daily to technical reading and active participation in developer communities')

tools = doc.add_paragraph(style='Normal Style')
tools.add_run('AI Tools Proficiency: ').bold = True
tools.add_run('Cursor, ChatGPT, Tongyi Lingma for advanced coding assistance and problem-solving')

# 保存文档
doc_path = os.path.join(os.path.dirname(__file__), '../英文简历/XIE_RONGFEI_Optimized_Frontend_Resume.docx')
doc.save(doc_path)
print(f"Optimized resume saved at: {doc_path}")