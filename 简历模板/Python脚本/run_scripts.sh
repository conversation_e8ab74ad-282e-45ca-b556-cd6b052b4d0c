#!/bin/bash

# 获取当前脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../.." &> /dev/null && pwd )"
PYTHON_ENV="$SCRIPT_DIR/resume_env/bin/python"

echo "======================="
echo "简历生成工具"
echo "======================="
echo "请选择要生成的简历类型:"
echo "1. 简洁版中文简历 (苹方字体)"
echo "2. 标准英文简历"
echo "3. 高级优化版英文简历"
echo "4. 退出"
echo "======================="

read -p "请输入选项 (1-4): " option

case $option in
    1)
        echo "正在生成简洁版中文简历..."
        $PYTHON_ENV "$SCRIPT_DIR/create_chinese_resume.py"
        ;;
    2)
        echo "正在生成标准英文简历..."
        $PYTHON_ENV "$SCRIPT_DIR/create_resume.py"
        ;;
    3)
        echo "正在生成高级优化版英文简历..."
        $PYTHON_ENV "$SCRIPT_DIR/create_optimized_resume.py"
        ;;
    4)
        echo "退出程序"
        exit 0
        ;;
    *)
        echo "无效的选项，请重新运行脚本"
        exit 1
        ;;
esac

echo "简历生成完成！" 