import os
from docx import Document
from docx.shared import Pt, Inches, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.ns import qn

# 创建文档对象
doc = Document()

# 字体文件路径
FONT_PATH = os.path.join(os.path.dirname(__file__), 'fonts/苹方-简.ttf')
FONT_NAME = '苹方-简'  # 使用字体文件的名称

# 设置中文字体支持 - 让中英文都使用同一字体
def set_run_font(run, font_name=FONT_NAME):
    # 同时设置英文和中文字体
    run.font.name = font_name  # 英文字体
    r = run._element.get_or_add_rPr()
    r.set(qn('w:eastAsia'), font_name)  # 中文字体

# 设置页面边距 - 更窄的边距
sections = doc.sections
for section in sections:
    section.top_margin = Inches(0.6)
    section.bottom_margin = Inches(0.6)
    section.left_margin = Inches(0.6)
    section.right_margin = Inches(0.6)

# 创建样式
styles = doc.styles

# 标题样式
title_style = styles.add_style('Title Style', WD_STYLE_TYPE.PARAGRAPH)
title_font = title_style.font
title_font.name = FONT_NAME  # 使用苹方简
title_font.size = Pt(14)
title_font.bold = True
title_font.color.rgb = RGBColor(0, 0, 0)  # 黑色
title_paragraph = title_style.paragraph_format
title_paragraph.space_before = Pt(8)
title_paragraph.space_after = Pt(4)

# 子标题样式
subtitle_style = styles.add_style('Subtitle Style', WD_STYLE_TYPE.PARAGRAPH)
subtitle_font = subtitle_style.font
subtitle_font.name = FONT_NAME  # 使用苹方简
subtitle_font.size = Pt(11)
subtitle_font.bold = True
subtitle_paragraph = subtitle_style.paragraph_format
subtitle_paragraph.space_before = Pt(6)
subtitle_paragraph.space_after = Pt(2)

# 正文样式
normal_style = styles.add_style('Normal Style', WD_STYLE_TYPE.PARAGRAPH)
normal_font = normal_style.font
normal_font.name = FONT_NAME  # 使用苹方简
normal_font.size = Pt(10)
normal_paragraph = normal_style.paragraph_format
normal_paragraph.space_before = Pt(2)
normal_paragraph.space_after = Pt(2)

# 项目符号样式
bullet_style = styles.add_style('Bullet Style', WD_STYLE_TYPE.PARAGRAPH)
bullet_font = bullet_style.font
bullet_font.name = FONT_NAME  # 使用苹方简
bullet_font.size = Pt(10)
bullet_paragraph = bullet_style.paragraph_format
bullet_paragraph.space_before = Pt(1)
bullet_paragraph.space_after = Pt(1)
bullet_paragraph.left_indent = Inches(0.15)

# 读取txt文件内容
txt_path = os.path.join(os.path.dirname(__file__), '../中文简历/六年前端-谢荣飞.txt')
with open(txt_path, 'r', encoding='utf-8') as f:
    content = f.readlines()

# 解析内容
current_section = None
section_content = {}
current_content = []

for line in content:
    line = line.strip()
    
    # 跳过空行
    if not line:
        continue
    
    # 检查是否是主要部分标题
    if line in ['个人信息', '教育背景', '开发技能', '工作经历', '项目经历', '自我介绍', '致谢']:
        if current_section:
            section_content[current_section] = current_content
        current_section = line
        current_content = []
    else:
        current_content.append(line)

# 添加最后一部分
if current_section and current_content:
    section_content[current_section] = current_content

# 添加个人信息 - 简化格式
if '个人信息' in section_content:
    info = section_content['个人信息']
    # 第一行作为标题 (姓名)
    p = doc.add_paragraph(style='Title Style')
    run = p.add_run(info[0])
    set_run_font(run)
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 其他信息放在一行
    contact_info = []
    for item in info[1:]:
        if item and not item.isspace():
            contact_info.append(item)
    
    if contact_info:
        contact_text = ' | '.join(contact_info)
        p = doc.add_paragraph(style='Normal Style')
        run = p.add_run(contact_text)
        set_run_font(run)
        p.alignment = WD_ALIGN_PARAGRAPH.CENTER

# 添加分隔线
p = doc.add_paragraph('_' * 60, style='Normal Style')
p.alignment = WD_ALIGN_PARAGRAPH.CENTER
run = p.runs[0]
set_run_font(run)

# 添加教育背景
if '教育背景' in section_content:
    p = doc.add_paragraph('教育背景', style='Title Style')
    run = p.runs[0]
    set_run_font(run)
    
    edu_info = ' | '.join(section_content['教育背景'])
    p = doc.add_paragraph(style='Normal Style')
    run = p.add_run(edu_info)
    set_run_font(run)

# 添加开发技能
if '开发技能' in section_content:
    p = doc.add_paragraph('核心技能', style='Title Style')
    run = p.runs[0]
    set_run_font(run)
    
    for item in section_content['开发技能']:
        p = doc.add_paragraph(style='Bullet Style')
        run = p.add_run('• ' + item)
        set_run_font(run)

# 添加工作经历
if '工作经历' in section_content:
    p = doc.add_paragraph('工作经历', style='Title Style')
    run = p.runs[0]
    set_run_font(run)
    
    work_items = section_content['工作经历']
    company = None
    position = None
    responsibilities = []
    
    for item in work_items:
        if '上海艾爵思' in item or '互诚信息' in item:  # 公司行
            if company:  # 如果已有公司，先处理之前的
                p = doc.add_paragraph(style='Subtitle Style')
                run = p.add_run(company)
                set_run_font(run)
                
                if position:
                    p.add_run(' — ')
                    run = p.add_run(position.replace('岗位：', '').replace('工作职责：', ''))
                    set_run_font(run)
                    run.italic = True
                
                for resp in responsibilities:
                    p = doc.add_paragraph(style='Bullet Style')
                    run = p.add_run('• ' + resp)
                    set_run_font(run)
                
                responsibilities = []
            
            company = item
        elif '工作职责' in item or '岗位' in item:
            position = item
        else:
            responsibilities.append(item)
    
    # 处理最后一个公司
    if company:
        p = doc.add_paragraph(style='Subtitle Style')
        run = p.add_run(company)
        set_run_font(run)
        
        if position:
            p.add_run(' — ')
            run = p.add_run(position.replace('岗位：', '').replace('工作职责：', ''))
            set_run_font(run)
            run.italic = True
        
        for resp in responsibilities:
            p = doc.add_paragraph(style='Bullet Style')
            run = p.add_run('• ' + resp)
            set_run_font(run)

# 添加项目经历 - 简化结构
if '项目经历' in section_content:
    p = doc.add_paragraph('项目经验', style='Title Style')
    run = p.runs[0]
    set_run_font(run)
    
    project_items = section_content['项目经历']
    current_project = None
    project_info = ""
    achievements = []
    
    for item in project_items:
        if item.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.')):  # 新项目
            # 处理之前的项目
            if current_project:
                # 项目标题
                p = doc.add_paragraph(style='Subtitle Style')
                run = p.add_run(current_project.replace('1.', '').replace('2.', '').replace('3.', '').replace('4.', '').replace('5.', '').replace('6.', '').replace('7.', '').strip())
                set_run_font(run)
                
                # 项目简介和技术栈
                if project_info:
                    p = doc.add_paragraph(style='Normal Style')
                    run = p.add_run(project_info)
                    set_run_font(run)
                
                # 成就和贡献
                for ach in achievements:
                    p = doc.add_paragraph(style='Bullet Style')
                    run = p.add_run('• ' + ach)
                    set_run_font(run)
                
                project_info = ""
                achievements = []
            
            current_project = item
        elif '项⽬简介' in item or '技术栈' in item:
            project_info += item + " "
        elif '技术产出' in item:
            continue  # 跳过这一行
        else:
            achievements.append(item)
    
    # 处理最后一个项目
    if current_project:
        # 项目标题
        p = doc.add_paragraph(style='Subtitle Style')
        run = p.add_run(current_project.replace('1.', '').replace('2.', '').replace('3.', '').replace('4.', '').replace('5.', '').replace('6.', '').replace('7.', '').strip())
        set_run_font(run)
        
        # 项目简介和技术栈
        if project_info:
            p = doc.add_paragraph(style='Normal Style')
            run = p.add_run(project_info)
            set_run_font(run)
        
        # 成就和贡献
        for ach in achievements:
            p = doc.add_paragraph(style='Bullet Style')
            run = p.add_run('• ' + ach)
            set_run_font(run)

# 添加自我介绍 - 合并为一段简洁的文字
if '自我介绍' in section_content:
    p = doc.add_paragraph('个人特质', style='Title Style')
    run = p.runs[0]
    set_run_font(run)
    
    # 合并为单个段落
    intro_text = ' '.join(section_content['自我介绍'])
    p = doc.add_paragraph(style='Normal Style')
    run = p.add_run(intro_text)
    set_run_font(run)

# 添加致谢
if '致谢' in section_content:
    p = doc.add_paragraph(style='Normal Style')
    for item in section_content['致谢']:
        run = p.add_run(item)
        set_run_font(run)
        run.italic = True
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER

# 保存文档
doc_path = os.path.join(os.path.dirname(__file__), '../中文简历/谢荣飞-前端开发简历-简洁版.docx')
doc.save(doc_path)
print(f"简洁版中文简历已保存至: {doc_path}") 