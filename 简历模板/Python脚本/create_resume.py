import os
from docx import Document
from docx.shared import Pt, Inches, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE

# 创建文档对象
doc = Document()

# 设置页面边距
sections = doc.sections
for section in sections:
    section.top_margin = Inches(0.8)
    section.bottom_margin = Inches(0.8)
    section.left_margin = Inches(0.8)
    section.right_margin = Inches(0.8)

# 创建样式
styles = doc.styles
# 名称样式
name_style = styles.add_style('Name Style', WD_STYLE_TYPE.PARAGRAPH)
name_font = name_style.font
name_font.name = 'Calibri'
name_font.size = Pt(18)
name_font.bold = True
name_font.color.rgb = RGBColor(0, 0, 0)

# 标题样式
title_style = styles.add_style('Title Style', WD_STYLE_TYPE.PARAGRAPH)
title_font = title_style.font
title_font.name = 'Calibri'
title_font.size = Pt(14)
title_font.bold = True
title_font.color.rgb = RGBColor(0, 51, 102)  # 深蓝色

# 子标题样式
subtitle_style = styles.add_style('Subtitle Style', WD_STYLE_TYPE.PARAGRAPH)
subtitle_font = subtitle_style.font
subtitle_font.name = 'Calibri'
subtitle_font.size = Pt(12)
subtitle_font.bold = True
subtitle_font.color.rgb = RGBColor(0, 0, 0)

# 正文样式
normal_style = styles.add_style('Normal Style', WD_STYLE_TYPE.PARAGRAPH)
normal_font = normal_style.font
normal_font.name = 'Calibri'
normal_font.size = Pt(11)
normal_paragraph = normal_style.paragraph_format
normal_paragraph.space_before = Pt(6)
normal_paragraph.space_after = Pt(6)

# 项目符号样式
bullet_style = styles.add_style('Bullet Style', WD_STYLE_TYPE.PARAGRAPH)
bullet_font = bullet_style.font
bullet_font.name = 'Calibri'
bullet_font.size = Pt(11)
bullet_paragraph = bullet_style.paragraph_format
bullet_paragraph.space_before = Pt(3)
bullet_paragraph.space_after = Pt(3)
bullet_paragraph.left_indent = Inches(0.25)

# 添加姓名和个人信息
name = doc.add_paragraph('XIE RONGFEI', style='Name Style')
name.alignment = WD_ALIGN_PARAGRAPH.CENTER

info = doc.add_paragraph('Shanghai, China | +86 17607006309 | <EMAIL>', style='Normal Style')
info.alignment = WD_ALIGN_PARAGRAPH.CENTER

# 添加分隔线
doc.add_paragraph('_' * 80, style='Normal Style').alignment = WD_ALIGN_PARAGRAPH.CENTER

# 添加专业摘要
doc.add_paragraph('PROFESSIONAL SUMMARY', style='Title Style')
summary = doc.add_paragraph('Frontend Engineer with 6 years of experience in designing and developing large-scale applications. Skilled in Vue, React, and cross-platform development. Expertise in performance optimization, component encapsulation, and full-stack troubleshooting.', style='Normal Style')

# 添加技术技能
doc.add_paragraph('TECHNICAL SKILLS', style='Title Style')
skills = [
    'JavaScript/TypeScript, HTML5, CSS3, Browser Principles, Network Protocols',
    'Vue2/3, React, UniApp, UView, Component Development',
    'Frontend Performance Optimization, Code/Build/Resource Optimization',
    'Webpack, Gulp, Vite, Scaffold Development',
    'Node.js, Linux Commands, CI/CD Pipelines',
    'AI-assisted Development (Cursor, ChatGPT, Tongyi Lingma)'
]

for skill in skills:
    p = doc.add_paragraph(style='Bullet Style')
    p.paragraph_format.left_indent = Inches(0.25)
    run = p.add_run('• ' + skill)

# 添加工作经验
doc.add_paragraph('WORK EXPERIENCE', style='Title Style')

# 第一份工作
job1 = doc.add_paragraph('Senior Frontend Engineer', style='Subtitle Style')
job1_details = doc.add_paragraph('Shanghai Aijuesi Information Technology Co., Ltd. | Dec 2020 - Feb 2025', style='Normal Style')
job1_details.paragraph_format.space_after = Pt(3)
doc.add_paragraph('Position: Frontend Development Lead', style='Normal Style').italic = True

job1_responsibilities = [
    'Led the full lifecycle management of multiple core products (PC, mobile H5, mini programs) including requirement analysis, technical solution design, development implementation, and deployment.',
    'Proposed frontend technical solutions for uncertain requirements, arranged development work, and led project performance optimization.',
    'Managed team technology selection, established unified frontend development standards, and improved team efficiency.',
    'Participated in technical sharing, trained frontend engineers, and promoted frontend best practices.'
]

for resp in job1_responsibilities:
    p = doc.add_paragraph(style='Bullet Style')
    p.paragraph_format.left_indent = Inches(0.25)
    run = p.add_run('• ' + resp)

# 第二份工作
job2 = doc.add_paragraph('Intermediate Frontend Engineer', style='Subtitle Style')
job2_details = doc.add_paragraph('Hucheng Information Technology (Shanghai) Co., Ltd. | July 2018 - Dec 2020', style='Normal Style')
job2_details.paragraph_format.space_after = Pt(3)

job2_responsibilities = [
    'Participated in frontend development and defect repair, handling complex interaction logic.',
    'Involved in component library encapsulation, analyzed project difficulties, and provided solutions.',
    'Refactored redundant modules and optimized project performance to ensure product quality.',
    'Collaborated with product, design, and backend teams to ensure continuous product improvement.'
]

for resp in job2_responsibilities:
    p = doc.add_paragraph(style='Bullet Style')
    p.paragraph_format.left_indent = Inches(0.25)
    run = p.add_run('• ' + resp)

# 添加教育背景
doc.add_paragraph('EDUCATION', style='Title Style')
edu = doc.add_paragraph('Jiangxi Polytechnic College | Electronic Information Engineering | Sept 2015 - June 2018', style='Normal Style')

# 添加项目经验
doc.add_paragraph('PROJECT EXPERIENCE', style='Title Style')

# 项目1
project1 = doc.add_paragraph('POC Project Management Platform', style='Subtitle Style')
doc.add_paragraph('Technology Stack: Vite + Vue ecosystem + Element-UI + Axios', style='Normal Style')
p1_achievements = [
    'Led implementation of multi-level permission control, designed and developed custom directives for permission verification.',
    'Implemented data dashboard caching to minimize API requests, decomposed functionality into modules for efficient iteration.',
    'Developed 20+ customizable components published as npm packages with 500+ downloads.',
    'Resolved memory leaks using Chrome DevTools, improving page performance during extended runtime.'
]

for ach in p1_achievements:
    p = doc.add_paragraph(style='Bullet Style')
    p.paragraph_format.left_indent = Inches(0.25)
    run = p.add_run('• ' + ach)

# 项目2
project2 = doc.add_paragraph('IT Department Asset Library System', style='Subtitle Style')
doc.add_paragraph('Technology Stack: React + Webpack + Redux + Echarts', style='Normal Style')
p2_achievements = [
    'Utilized Higher Order Components to implement custom components through property passing.',
    'Implemented virtual lists for large datasets, improving rendering performance by 50% and reducing memory usage by 60%.',
    'Optimized Echarts charts, reducing loading time from 2000ms to 65ms for large datasets.',
    'Led schema design for complex form logic, implementing plugin mechanism for form items and rules.'
]

for ach in p2_achievements:
    p = doc.add_paragraph(style='Bullet Style')
    p.paragraph_format.left_indent = Inches(0.25)
    run = p.add_run('• ' + ach)

# 项目3
project3 = doc.add_paragraph('Shanghai City Regulations Compendium (PC + H5)', style='Subtitle Style')
doc.add_paragraph('Technology Stack: Webpack4 + Vue ecosystem + Axios', style='Normal Style')
p3_achievements = [
    'Optimized complex form pages, improving first-screen loading speed by 30%.',
    'Utilized canvas for image compression, improving upload speed by 65% and reducing package size by 40%.',
    'Performed deep encapsulation of Axios, handling token transmission, configurations, and exceptions.',
    'Promoted Git commit specifications using ESLint, Prettier, and Husky for code quality assurance.'
]

for ach in p3_achievements:
    p = doc.add_paragraph(style='Bullet Style')
    p.paragraph_format.left_indent = Inches(0.25)
    run = p.add_run('• ' + ach)

# 添加自我介绍
doc.add_paragraph('ADDITIONAL INFORMATION', style='Title Style')
info_text = [
    'Won second prize in a C language programming competition in college.',
    'Maintain 1.5 hours of daily technical reading and active participation in tech communities.',
    'Focus on JavaScript fundamentals with solid understanding of core concepts.',
    'Enjoy sports including swimming, running, basketball, badminton, and table tennis.'
]

for info in info_text:
    p = doc.add_paragraph(style='Bullet Style')
    p.paragraph_format.left_indent = Inches(0.25)
    run = p.add_run('• ' + info)

# 保存文档
doc_path = os.path.join(os.path.dirname(__file__), '../英文简历/XIE_RONGFEI_Frontend_Resume.docx')
doc.save(doc_path)
print(f"Resume saved at: {doc_path}")