#!/usr/bin/env python3
"""
更新Python脚本中的文件路径。
用于修复简历生成脚本中的路径，使其适应新的目录结构。
"""

import os
import re
import sys

def update_file_paths(file_path):
    """更新指定文件中的路径引用"""
    
    # 获取项目根目录和当前文件所在目录
    script_dir = os.path.dirname(os.path.abspath(file_path))
    project_root = os.path.abspath(os.path.join(script_dir, "../.."))
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义路径映射规则
    path_mappings = {
        # 中文简历路径更新
        r'六年前端-谢荣飞\.txt': r'../中文简历/六年前端-谢荣飞.txt',
        r'谢荣飞-前端开发简历-简洁版\.docx': r'../中文简历/谢荣飞-前端开发简历-简洁版.docx',
        r'谢荣飞-前端开发简历-中文版\.docx': r'../中文简历/谢荣飞-前端开发简历-中文版.docx',
        
        # 英文简历路径更新
        r'Frontend-Resume-XieRongfei-EN\.txt': r'../英文简历/Frontend-Resume-XieRongfei-EN.txt',
        r'XIE_RONGFEI_Frontend_Resume\.docx': r'../英文简历/XIE_RONGFEI_Frontend_Resume.docx',
        r'XIE_RONGFEI_Optimized_Frontend_Resume\.docx': r'../英文简历/XIE_RONGFEI_Optimized_Frontend_Resume.docx',
    }
    
    # 执行替换
    updated_content = content
    for pattern, replacement in path_mappings.items():
        updated_content = re.sub(
            r'([\'\"])(' + pattern + r')([\'\"]\s*\))',
            r'\1' + replacement + r'\3',
            updated_content
        )
    
    # 检查是否有更改
    if content != updated_content:
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        print(f"已更新文件: {file_path}")
        return True
    else:
        print(f"文件路径已是最新: {file_path}")
        return False

def main():
    # 获取当前脚本所在的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 要更新的Python脚本文件
    script_files = [
        os.path.join(current_dir, "create_chinese_resume.py"),
        os.path.join(current_dir, "create_resume.py"),
        os.path.join(current_dir, "create_optimized_resume.py")
    ]
    
    # 更新每个文件中的路径
    updated_count = 0
    for file in script_files:
        if os.path.exists(file):
            if update_file_paths(file):
                updated_count += 1
        else:
            print(f"找不到文件: {file}")
    
    print(f"处理完成! 更新了 {updated_count}/{len(script_files)} 个文件。")

if __name__ == "__main__":
    main() 