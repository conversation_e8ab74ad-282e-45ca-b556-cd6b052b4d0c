# PERSONAL INFORMATION

<PERSON><PERSON> (Male) | Born 1996 | Frontend Engineer | Shanghai
Mobile: +86 17607006309
Email: <EMAIL>
Professional Experience: 6 years

# EDUCATION

Jiangxi Polytechnic College | Electronic Information Engineering | Sept 2015 - June 2018

# TECHNICAL SKILLS

- 6 years of frontend development experience with expertise in large-scale frontend project design and development, full-stack troubleshooting capabilities, and extensive experience in encapsulating and extracting reusable components
- Solid understanding of JavaScript and TypeScript, familiar with browser underlying principles and common network protocols
- Proficient in Vue2/3 and React frameworks, skillful in utilizing their ecosystems for project development with in-depth knowledge of their internal implementations
- Experienced in cross-platform development using UniApp and the UView framework with relevant project experience
- Well-versed in frontend performance optimization strategies including code optimization, build optimization, and resource optimization, capable of implementing optimizations based on specific business scenarios
- Proficient with frontend build tools such as Webpack, Gulp, and Vite, with scaffold development experience
- Leveraging AI tools like Cursor, ChatGPT, and Tongyi Lingma to assist code writing, improve coding efficiency, and solve development problems
- Familiar with Node.js, Linux basic commands, and CI/CD workflows, capable of independently deploying and maintaining projects

# WORK EXPERIENCE

## Senior Frontend Engineer | Development Team | Shanghai Aijuesi Information Technology Co., Ltd. | Dec 2020 - Feb 2025
**Position: Frontend Development Lead**

**Responsibilities:**
- Led the full lifecycle management of multiple core products (PC, mobile H5, mini programs) including requirement analysis, technical solution design, development implementation, and deployment, delivering high-quality projects on schedule
- Proposed frontend technical solutions for uncertain requirements, arranged related development work, and led project performance optimization, solving technical and business challenges during project development
- Shouldered team technology selection responsibilities, managed company frontend infrastructure, established unified frontend development standards, and improved team development efficiency and collaboration capabilities
- Participated in technical sharing and communication, trained frontend engineers, promoted frontend best practices, and enhanced the technical capabilities of the frontend team

## Intermediate Frontend Engineer | Advertising Platform | Hucheng Information Technology (Shanghai) Co., Ltd. | July 2018 - Dec 2020

**Responsibilities:**
- Participated in the frontend development and defect repair of company products, handled complex interaction logic, and efficiently and accurately implemented product requirements
- Involved in common component library encapsulation and other work, discussed and analyzed the difficulties in projects, and provided reasonable suggestions
- Refactored redundant modules and optimized project performance to ensure product quality and improve maintainability
- Closely communicated with product, design, and backend teams, collaborating in iterations and other aspects to ensure continuous product improvement

# PROJECT EXPERIENCE

## 1. POC Project Management Platform

**Project Overview:** The China Merchants Credit Card Center Project Management Office needed a platform to better manage requirements from other departments through major vendors. Starting with the creation of project application forms, different roles have different approval information and display content, requiring the development of a complete dynamic approval process.

**Technology Stack:** Vite + Vue ecosystem + Element-UI + Axios

**Technical Achievements:**
- Led the implementation of multi-level permission control based on product requirements, independently designed and developed permission control methods, encapsulated custom directives to implement permission verification and interaction prompts, ensuring each role has appropriate operation permissions at different process states
- Implemented data dashboard caching to minimize API requests, decomposed functionality into single modules, and encapsulated algorithm functions based on node polymorphism to enable more efficient iterations in subsequent approval process changes
- Evaluated open-source component libraries, designed and developed 20+ customizable components (various forms, tables, advanced search, etc.), published as npm packages for team use with 500+ downloads
- Used Chrome DevTools to detect and troubleshoot memory leaks, resolving page lag issues during extended runtime and improving page fluidity

## 2. IT Department Asset Library System

**Project Overview:** A system created to help China Merchants Bank Credit Card Center review the working regulations, process systems, and latest information updates of various subsystems under the department, consisting of both a backend management system and client interface.

**Technology Stack:** React + Webpack + Redux + Echarts

**Technical Achievements:**
- Utilized Higher Order Components to separate components into base components and configurable components, then nested configurable components into base components to implement custom components through property passing
- Implemented virtual lists to solve page rendering lag caused by thousands of data records, optimizing rendering performance to ensure smoothness with large datasets, improving page rendering performance by 50% and reducing memory usage by 60%
- Optimized Echarts charts to resolve rendering lag with large datasets, reducing loading time from 2000ms to 65ms, significantly enhancing user experience
- Led the schema design for general forms and complex form logic processing, implementing a plugin mechanism for plug-and-play form items and rule integration

## 3. Tong-e-Sign Mini Program

**Project Overview:** Provides buying, selling, and processing contract drafting templates, sending and receiving vouchers, comparing contract revision information, and performance information.

**Technology Stack:** UniApp framework

**Technical Achievements:**
- Secondary encapsulation of uni.request based on Promise, implementing unified error catching, loading timeout, loading prompts, and other functions
- Streamlined the project, identified existing issues, and provided corresponding optimization and solution strategies, including functional modules, performance issues, and security vulnerabilities
- Encapsulated custom components such as search pages, tab bars, and cards to implement component reuse and improve code efficiency
- Introduced skeleton screens and encapsulated loading in certain pages to further resolve blank screens and enhance user experience

## 4. Shanghai City Regulations Compendium (One-Point Universal PC + H5)

**Project Overview:** A system established to help Shanghai citizens better understand and query government-issued regulations and rules, with frontend features including advanced search, popular searches, regulation recommendations, legislative dynamics, subject services, thematic indexing, version switching, regulation-specific QR codes, regulation downloads, intelligent Q&A, and data tracking.

**Technology Stack:** Webpack4 + Vue ecosystem + Axios

**Technical Achievements:**
- Optimized complex form and large data query pages, improving first-screen loading speed by 30% and enhancing user operation experience
- Utilized canvas to compress QR code image sizes, reducing upload bandwidth pressure and improving upload speed by 65%; optimized images, implemented on-demand component importing, and extracted common JS with Webpack, reducing package size by 40%
- Performed deep secondary encapsulation of Axios, configuring request interceptors and response interceptors to uniformly handle interface token transmission, custom configurations, and data prompts, while providing unified handling for HTTP exceptions and business exceptions
- Promoted Git commit specification usage in projects, implementing code submission checks through prettier/eslint combined with husky+commitlint+githook to ensure unified code style and standards within the team, guaranteeing code quality

## 5. One-Point Universal Backend

**Project Overview:** A system that unifies the compilation, classification, organization, and dynamic updating of local regulations and government rules for legal engineers, managing various roles, permissions, configuration modules, visitor traffic, logs, and other aspects.

**Technology Stack:** Vue ecosystem (Vue + Element + vue-router + Vuex + fetch + TS) + Webpack4 + Echarts for data visualization

**Responsibilities:**
- Implemented the technical solution using the Vue ecosystem and Webpack4, with Echarts for data visualization
- Responsible for building the project environment module UI, with the project divided into 13 modules requiring component encapsulation for component-based development of each module
- Mainly responsible for developing statistical analysis, regulation management, data processing, authority management, and user management modules
- Used Echarts for data visualization in statistical analysis, implementing time-based search and display
- Implemented data management using Vuex in regulation management, with vue-tinymac rich text editor for creating and editing regulations
- Encapsulated data processing functions for data processing and authority management, implementing browser-like ctrl+f convenient search functionality for large data volumes
- Implemented user management features allowing adding, deleting, and editing user permissions, with custom Vue directives to display features available to each user

## 6. Huasheng Mall

**Project Overview:** Huasheng Department Store is a website providing daily necessities for the general public.

**Role:** Frontend Development Lead

**Responsibilities:**
- Developed the project using Vue framework, mainly responsible for the homepage and login/registration page development
- Implemented login and registration page functionality
- Used Vue + Vuex + Axios for login/registration page permission interception and data rendering
- Utilized Vue Router to build routing and implement page navigation
- Used Sass for CSS preprocessing

## 7. Kaidi International K12

**Project Overview:** A system specifically designed to solve communication and management issues between teachers and students' parents, including barrier-free school-family communication, performance analysis, homework notifications, and publishing, primarily responsible for developing class lists, classroom evaluation lists, and comment lists.

**Role:** Frontend Development Lead

**Responsibilities:**
- Primarily developed using Layui and jQuery
- Implemented homepage architecture and jQuery page animation effects
- Retrieved class list data through AJAX communication with the backend
- Used Layui's iframe layer combined with mobile pages to create preview effects

# SELF-INTRODUCTION

I first encountered programming in college and developed an interest in it, cultivating good learning habits. I participated in a C language programming competition and won second prize. At work, I record my learning and work notes, steadily growing professionally.

I maintain a continuous passion for technology and consistently learn and share frontend technologies. I focus on learning JavaScript fundamentals, with relatively solid understanding of JS fundamentals.

I have a habit of reading technical books, maintaining 1.5 hours of reading time daily. I am active in technical communities and follow cutting-edge technology trends.

I have an outgoing personality and strong pressure resistance. I enjoy sports such as swimming, running, basketball, badminton, and table tennis.

# ACKNOWLEDGEMENT

Thank you for taking the time to read my resume. I look forward to the opportunity of working with you.