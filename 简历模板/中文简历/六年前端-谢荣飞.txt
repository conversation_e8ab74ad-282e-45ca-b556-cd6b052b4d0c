个人信息

谢荣飞/男/1996/前端/上海
手机：17607006309
Email：<EMAIL> 
相关工作经验：6年

教育背景
江西工业职业技术学院		电子信息工程		2015.9-2018.6	

开发技能
六年前端开发经验，具有完整大型前端项目设计、研发，具备全链路问题排查能力，对封装和提取通用的组件有丰富的经验
理解并掌握 JavaScript 和 TypeScript，熟悉浏览器底层原理(渲染机制、事件循环)与常见网络协议(HTTP/HTTPS、WebSocket)
精通Vue2/3、React框架，深入研究其内部实现原理(虚拟DOM、响应式系统、Diff算法)，能高效使用其生态系统解决复杂业务问题
熟悉UniApp跨端开发，基于UView框架完成多个商业项目，具备一套代码多端部署的实践经验
擅长前端性能优化，包括代码分割、懒加载、缓存策略、资源压缩等，能结合实际业务场景进行优化
精通Webpack、Vite等构建工具，熟练进行性能调优，有过脚手架开发经验
利用Cursor、ChatGPT、通义灵码等AI工具，辅助代码编写，提升编码效率，解决开发过程中的问题。
熟悉Node.js后端开发，掌握Linux服务器管理及CI/CD自动化部署流程，实现完整前端工程体系建设

工作经历
2020/12 - 至今  上海艾爵思信息科技有限公司		开发组		高级前端开发工程师	
岗位：前端开发负责⼈
工作职责：
负责公司产品线多个核心项目（PC端、移动端H5、小程序等）的全生命周期管理，包括需求分析、技术方案设计、开发实现到部署上线，按时高质量交付项目。
针对不确定需求提出前端技术方案，安排相关开发工作并负责项目性能优化，解决项目开发过程中遇到的技术和业务难题
承担团队技术选型⼯作，负责公司前端基础建设，制定前端统一开发规范，推动团队的开发效率和协作能力提升
参与技术分享和交流，培训前端工程师，推广前端最佳实践，提升前端团队的技术能力

2018/07 - 2020/12	互诚信息技术（上海）有限公司	广告平台	中级前端开发工程师
工作职责：
参与公司产品的前端开发与缺陷修复，处理复杂交互逻辑，高效、准确地实现产品需求
参与公共组件库封装等工作，对项目中存在的重难点进行讨论分析，提出合理的建议
对冗余模块进行代码重构，对项目进行性能优化，保证产品质量，提高可维护性
与产品、设计、后端团队密切沟通，在迭代等环节协同合作，保障产品持续完善



项目经历

1. POC项⽬管理平台

项⽬简介：招商信⽤卡中⼼项⽬管理室为了更好管理其他部⻔的需求通过各⼤⼚商来开发项⽬。通过项⽬申请表单的创建开始，不同⻆⾊的审批信息以及展⽰内容不同，编写整套动态审批流程。
技术栈：Vite + Vue3 + TypeScript + Element-Plus + Axios
技术产出：
主导实现业务的多级权限控制，根据产品需求独立设计权限控制方式并落地开发，封装自定义指令实现权限校验与交互提示，实现每个角色在不同流程状态下有不同的操作权限
数据大屏通过缓存数据实现接⼝请求的最⼩化，通过将功能拆分成单⼀模块，根据节点多态情况封装成算法函数，实现在后续的审批流程变更更⾼效的迭代
对开源组件库进行评估，设计和开发可定制化组件20+（多种类表单、表格，高级搜索等），并以npm包的形式进行发布供团队使用，下载量500
使用Chrome调试器功能对项目中的内存泄漏进行检测摸排，解决长时间运行页面卡顿问题，提高页面流畅度

2. 信息技术部资产库系统
项⽬简介：为了能更⽅便招商银⾏信⽤卡中⼼审阅该部⻔下的各个⼦系统的⼯作章程规范流程体系以及信息的最新动态，该系统是⼀套拥有后台管理系统和客⼾端界⾯的系统。
技术栈：React + Redux + TypeScript + Webpack + Echarts
技术产出：
使用高阶组件，将组件分离成基础组件和可配置组件，然后将可配置组件嵌套到基础组件中，通过属性传递的方式实现自定义组件
使用虚拟列表解决因为万条数据渲染而导致的页面渲染卡顿问题，优化渲染性能，确保在大量数据场景下依然流畅，页面渲染性能提升50%，内存占用减少60%
优化Echarts图表解决渲染大数据量导致卡顿加载时间慢等问题，使得对大量数据加载时间从2000ms提升至65ms，大大提升用户的体验感
主导通用表单及复杂表单逻辑处理的 schema 设计，运用插件化机制实现插拔式表单项与规则接入

3. 桐e签小程序

项⽬简介：提供买卖加工类合同起草模板，收发凭证，对比合同修改信息，履约信息。面向企业用户的电子合同管理平台。
技术栈：UniApp + Vue + UView + 微信小程序API
技术产出：
基于Promise封装uni.request网络层，实现统一错误处理、重试机制、超时控制和加载提示，API调用错误率降低85%
主导项目架构重构，梳理25个功能模块性能瓶颈，实现包体积优化方案，小程序包大小减少40%，启动时间缩短60%
开发10+高质量UI组件(搜索、Tab栏、卡片等)，在3个业务项目中复用，提升团队开发效率40%
实现骨架屏和智能loading策略，首屏白屏时间减少75%，用户留存率提升15%，获得产品经理和用户高度评价

4. 上海城市法规全书（一点通用PC+H5）

项⽬简介：为了让上海市民更好了解查询政府发布的法规、规章而建立的系统，前台功能包括高级搜索功能、热门搜索、法规推荐立法动态、主体服务、主题索引、版本切换、法规详细二维码、法规下载、智能问答、数据埋点等功能。
技术栈：Vue + Vuex + Webpack4 + Axios + Element UI
技术产出：
优化复杂表单大数据量查询页面，提高首屏加载速度30%，提升用户操作体验
利用canvas压缩二维码图片大小，减少上传文件带宽压力,提高上传速度65%；通过优化图片、按需引入组件、webpack提取公共JS，打包文件体积减少40%
对Axios进行了深度的二次封装，配置请求拦截器和响应拦截器 ，统一处理了接口token传递、自定义配置、数据提示等，并对Http异常和业务异常进行统一处理 
推动项⽬使⽤ Git commit提交规范，通过prettier/eslint配合husky+commitlint+githook的⽅式实现代码提交检查，确保团队内部代码⻛格和代码规范的统⼀，保证代码质量

5. 一点通用后台

项⽬简介：将地方性法规、政府规章进行统一汇编、分类整理、动态更新，方便法律工程师进行汇编。让系统的各个⻆⾊，权限，配置模块，以及访问量，⽇志等进⾏管理。
技术栈：Vue + TypeScript + Vuex + Element UI + Echarts + Webpack4
技术产出：
主导项目架构设计，将系统拆分为13个独立模块，采用组件化开发策略，提高代码复用率65%，降低模块间耦合度
开发统计分析模块，利用Echarts实现8种数据可视化图表，支持多维度时间筛选，数据加载性能提升70%
设计法规管理富文本编辑系统，基于Vue-tinymce实现专业编辑器，支持复杂法规格式和批量处理，提升编辑效率40%
实现高性能数据处理引擎，处理10万+条规章数据，开发类似浏览器ctrl+f的快速搜索功能，检索速度提升85%
开发基于Vue自定义指令的细粒度权限控制系统，实现精确到按钮级别的权限管理，增强系统安全性和用户体验

6. 华盛商城

项⽬简介：华盛百货是一款面向全体人群，为用户提供日常生活用品的电商网站。
技术栈：Vue + Vuex + Axios + Vue Router + Sass
技术产出：
负责首页和用户认证系统开发，实现高转化率的商品展示和安全可靠的用户登录注册功能
设计实现基于Vuex的全局状态管理方案，与权限拦截系统集成，实现精确的页面访问控制和数据渲染
优化路由配置，实现智能懒加载策略，页面切换时间减少70%，提升用户浏览体验
采用Sass预处理器构建可维护的CSS架构，实现主题定制功能，满足不同季节和活动的页面风格需求

7. 启德国际K12系统

项⽬简介：专门解决老师和学生家长沟通和管理问题，主要包括无障碍家校沟通、成绩分析、作业通知和编发等，主要负责班级列表、课堂评价列表、点评列表的开发。
技术栈：jQuery + Layui + Ajax + 响应式设计
技术产出：
设计实现移动优先的响应式架构，使系统在各种设备上都有良好体验，用户满意度提升45%
开发高性能的班级列表和评价系统，支持实时数据更新和复杂筛选，操作响应时间控制在100ms内
利用jQuery实现丰富的动画效果和交互体验，增强用户粘性，活跃用户数增长30%
创新使用Layui的iframe弹层技术，实现移动端与PC端的统一预览功能，减少50%开发工作量

自我介绍

工程师背景：在校期间曾获C语言编程大赛二等奖，奠定了扎实的编程基础。六年工作中，始终保持学习成长，擅长解决复杂技术难题和业务挑战。

技术热情：每天保持1.5小时技术阅读习惯，深入研究JavaScript底层原理，活跃于技术社区，持续跟踪前沿技术发展。

个人特质：性格开朗，抗压能力强，善于团队协作和技术沟通。热爱运动(游泳、跑步、篮球等)，保持身心健康和积极心态。

致谢

感谢您花时间阅读我的简历，期待能有机会和您共事
