开发技能
1、五年前端开发经验，具有完整大型前端项目设计、研发，具备全链路问题排查能力，对封装和提取通用的组件有丰富的经验
2、理解并掌握 JavaScript 和 TypeScript，熟悉浏览器底层原理与常见的网络协议
3、熟练使用Vue2/3、React框架，熟练使用其周边生态进行项目开发并深入研究过其内部实现
4、熟悉uniapp，有跨端开发经验, 掌握用 uview 框架开发且有相关项目开发经验
5、熟悉前端性能优化策略，例如代码优化、打包优化、资源优化，能结合实际业务场景进行优化。
6、熟练使用 Webpack、Gulp、Vite 等前端构建工具，有过脚手架开发经验
7、利用chatgpt、通义灵码等AI工具，辅助代码编写，提升编码效率，解决开发过程中的问题。 
9、熟悉Node.js，熟悉Linux基本命令及CI/CD流程，能够独立完成项目的部署和维护。 

工作经历
2020/12 - 2025/02  上海艾爵思信息科技有限公司		开发组		中高级前端开发工程师	
岗位：前端开发负责⼈
工作职责：
1、负责公司产品线多个核心项目（PC端、移动端H5、小程序等）的全生命周期管理，包括需求分析、技术方案设计、开发实现到部署上线，按时高质量交付项目
2、针对不确定需求提出前端技术方案，安排相关开发工作并负责项目性能优化，解决项目开发过程中遇到的技术和业务难题
3、承担团队技术选型⼯作，负责公司前端基础建设，制定前端统一开发规范，推动团队的开发效率和协作能力提升
4、参与技术分享和交流，培训前端工程师，推广前端最佳实践，提升前端团队的技术能力

2018/07 - 2020/12	互诚信息技术（上海）有限公司	广告平台	中级前端开发工程师
工作职责：
1、参与公司产品的前端开发与缺陷修复，处理复杂交互逻辑，高效、准确地实现产品需求
2、参与公共组件库封装等工作，对项目中存在的重难点进行讨论分析，提出合理的建议
3、对冗余模块进行代码重构，对项目进行性能优化，保证产品质量，提高可维护性
4、与产品、设计、后端团队密切沟通，在迭代等环节协同合作，保障产品持续完善 。


项目经历
不要写常规的vue工具使用以及简单的功能实现，帮你挖掘了一些亮点；详细写4个项目即可，其他同质化项目简单说明


上海城市法规全书（一点通用PC+H5）		2023.8-2025.2  前端开发负责⼈
项⽬描述：为了让上海市民更好了解查询政府发布的法规、规章而建立的系统，前台功能包括高级搜索功能、热门搜索、法规推荐立法动态、主体服务、主题索引、版本切换、法规详细二维码、法规下载、智能问答、数据埋点等功能
技术栈：webpack4+vue全家桶 + axios
技术产出：
1、优化复杂表单大数据量查询页面，提高首屏加载速度30%，提升用户操作体验。 
2、利用canvas压缩二维码图片大小，减少上传文件带宽压力,提高上传速度65%；通过优化图片、按需引入组件、webpack提取公共JS,打包文件体积减少40%。 
3、对Axios进行了深度的二次封装，配置请求拦截器和响应拦截器 ，统一处理了接口token传递、自定义配置、数据提示等，并对Http异常和业务异常进行统一处理
4、推动项⽬使⽤ Git commit提交规范，通过prettier/eslint配合husky+commitlint+githook的⽅式实现代码提交检查，确保团队内部代码⻛格和代码规范的统⼀，保证代码质量


POC项⽬管理平台		2023.2-2023.8  前端开发负责⼈
项目描述：招商信⽤卡中⼼项⽬管理室为了更好管理其他部⻔的需求通过各⼤⼚商来开发项⽬。 通过项⽬申请表单的创建开始，不同⻆⾊的审批信息以及展⽰内容不同，编写整套动态审批流程。
技术栈：webpack4+vue全家桶+Element-ui+ajax
技术产出：
1、主导实现业务的多级权限控制，根据产品需求独立设计权限控制方式并落地开发，封装自定义指令实现权限校验与交互提示，实现每个角色在不同流程状态下有不同的操作权限
2、数据大屏通过缓存数据实现接⼝请求的最⼩化，通过将功能拆分成单⼀模块，根据节点多态情况封装成算法函数，实现在后续的审批流程变更更⾼效的迭代
3、 对开源组件库进行评估，设计和开发可定制化组件20+（多种类表单、表格，高级搜索等），并以npm包的形式进行发布供团队使用，下载量500+	
4、使用Chrome调试器功能对项目中的内存泄漏进行检测摸排，解决长时间运行页面卡顿问题，提高页面流畅度

信息技术部资产库系统	2022.6-2023.1  前端开发负责⼈
项⽬简介：为了能更⽅便招商银⾏信⽤卡中⼼审阅该部⻔下的各个⼦系统的⼯作章程规范流 程体 系以及信息的最新动态，该系统是⼀套拥有后台管理系统和客⼾端界⾯的系统。
技术栈：react + webpack + redux + Echarts
技术产出：
1、使用高阶组件，将组件分离成基础组件和可配置组件，然后将可配置组件嵌套到基础组件中，通过属性传递的方式实现自定义组件
2、使用虚拟列表解决因为万条数据渲染而导致的页面渲染卡顿问题，优化渲染性能，确保在大量数据场景下依然流畅，页面渲染性能提升50%，内存占用减少60%
3、优化Echarts图表解决渲染大数据量导致卡顿加载时间慢等问题，使得对大量数据加载时间从2000ms提升至65ms，大大提升用户的体验感
4、主导通用表单及复杂表单逻辑处理的 schema 设计，运用插件化机制实现插拔式表单项与规则接入

桐e签（小程序）2022.7-2023.1   前端开发负责⼈
项⽬描述：提供买卖加工类合同起草模板，收发凭证，对比合同修改信息，履约信息，在项目中负责首页，填写发货单页，合同列表页，语音识别等页面开发
技术栈：使用uniapp框架开发
技术产出：
1、基于 promise 的 uni.request 进行二次封装，实现统一错误捕捉、加载超时、loading 提示等功能；
2、对项目进行梳理，列出存在的问题清单，并输出相应的优化方案和解决方案，包括功能模块、性能问题、安全漏洞等
3、封装搜索页面、Tab 栏、卡片等自定义组件，实现组件的复用，提高代码效率
4、部分页面引入骨架屏和封装loading，进一步解决白屏和一定程度上提升了用户体验


其他项目：启德国际k12、华盛商城（pc）、一点通用后台管理系统





面试大师提示词
你是一位资深面试官和招聘专家，擅长分析简历并提出针对性问题。请根据以下简历内容进行分析，并提供有深度的面试问题：
先总结候选人的核心资质、技能和经验
分析简历中的关键项目经历，识别其中的优势和可能需要进一步探讨的地方
针对简历中的关键点提出10个高质量问题，分为以下类别：
技术能力验证问题（5个）
项目经历深度提问（5个）
软技能和团队协作问题（3个）
职业发展和动机问题（3个）
对简历中可能存在的疑点或不一致之处提出建议性问题
提供3个情景模拟问题，考察候选人在实际工作场景中的应变能力
以上问题应当有足够的针对性和深度，避免表面和通用的问题。如有必要，可以提供期望回答的要点，帮助评估候选人的回答质量。

