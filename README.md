# 前端开发简历与面试资料

这个项目提供了前端开发相关的简历模板和面试准备资料。

## 项目结构

```
.
├── 简历模板/           - 简历生成工具和模板文件
│   ├── Python脚本/    - 生成不同格式简历的Python脚本
│   │   └── resume_env/ - Python虚拟环境
│   ├── 中文简历/      - 中文简历文件
│   └── 英文简历/      - 英文简历文件
│
├── 面试资料/           - 前端面试相关资料
│   ├── 前端面试问题与参考答案.md
│   ├── 面试题与答案.md
│   └── 前端开发高级面试精要.md
```

## 使用指南

### 简历生成

使用Python脚本可以从文本文件快速生成精美的Word格式简历:

```bash
# 使用交互式菜单（推荐）
./简历模板/Python脚本/run_scripts.sh

# 或直接执行特定脚本
./简历模板/Python脚本/resume_env/bin/python 简历模板/Python脚本/create_chinese_resume.py
```

详细说明请参考 [简历模板/README.md](简历模板/README.md)

### 面试准备

面试资料目录包含前端开发相关的面试题和参考答案，涵盖基础到高级的各种主题。

详细说明请参考 [面试资料/README.md](面试资料/README.md)

## 环境配置

项目使用Python虚拟环境，已配置好所需的python-docx库。使用脚本时可通过以下方式运行:

```bash
# 使用便捷脚本
./简历模板/Python脚本/run_scripts.sh

# 或直接使用Python环境
./简历模板/Python脚本/resume_env/bin/python [脚本路径]
``` 